import type { Express } from "express";
import { createServer, type Server } from "http";
import { WebSocketServer, WebSocket } from "ws";
import { storage } from "./storage";
import { db } from "./db";
import { eq } from "drizzle-orm";
import { subscriptionMigrator } from "./lib/subscription-migrator";
import {
  migrationEndpoint,
  migrationStatusEndpoint,
  checkMigrations,
  ensureDataIntegrity
} from "./middleware/subscription-middleware";
import {
  insertLockerSchema,
  insertBookingSchema,
  insertTaskSchema,
  insertChatMessageSchema,
} from "@shared/schema";
import * as schema from "@shared/schema";
import { Request, Response } from 'express';

// Import Swift Agent Service
import { swiftAgentService } from "./services/swiftAgentService";

// Security imports
import {
  validationSchemas,
  handleValidationErrors,
  passwordUtils,
  jwtUtils
} from "./security/config";
import {
  requireAdmin,
  bruteForceProtection,
  accountLockoutProtection,
} from "./security/auth";
import {
  securityLogger,
  createSecurityEvent,
  SecurityEventType,
  getSecurityDashboard
} from "./security/monitoring";

// First, let's add a type declaration to extend the Express Session
declare module 'express-session' {
  interface SessionData {
    token?: string;
    userId?: string;
  }
}

export async function registerRoutes(app: Express): Promise<Server> {
  const httpServer = createServer(app);

  // Run automatic subscription migrations on startup
  console.log("🔄 Running automatic subscription migrations...");
  try {
    const migrationResult = await subscriptionMigrator.runMigrations();
    if (migrationResult.success) {
      console.log("✅ Subscription migrations completed successfully");
      if (migrationResult.tablesCreated.length > 0) {
        console.log(`📋 Tables created: ${migrationResult.tablesCreated.join(', ')}`);
      }
      if (migrationResult.columnsAdded.length > 0) {
        console.log(`📝 Columns added: ${migrationResult.columnsAdded.join(', ')}`);
      }
    } else {
      console.error("❌ Subscription migrations failed:", migrationResult.message);
    }
  } catch (error) {
    console.error("❌ Critical error during subscription migrations:", error);
  }

  // WebSocket server for real-time updates
  const wss = new WebSocketServer({ server: httpServer, path: "/ws" });
  
  // Add a dedicated WebSocket server for ESP32 devices
  const esp32Wss = new WebSocketServer({ server: httpServer, path: "/esp32/ws" });
  
  // Track connected ESP32 devices by locker ID
  const esp32Clients = new Map<number, WebSocket>();
  
  esp32Wss.on("connection", (ws) => {
    console.log("ESP32 device connected");
    let lockerId: number | null = null;
    
    ws.on("message", async (message) => {
      try {
        const data = JSON.parse(message.toString());
        console.log("ESP32 message received:", data);
        
        // Register the ESP32 device with its locker ID
        if (data.type === "register") {
          lockerId = parseInt(data.lockerId);
          esp32Clients.set(lockerId, ws);
          console.log(`ESP32 for locker ${lockerId} registered`);

          // Update locker status and ping time in database
          await storage.updateLockerStatus(lockerId, "connected");
          await storage.updateLockerPing(lockerId);

          // Log connection activity
          await storage.createActivity({
            type: "maintenance",
            message: `ESP32 for locker ${lockerId} connected`,
            lockerId,
            metadata: JSON.stringify({ connectionTime: new Date().toISOString() }),
          });

          // Broadcast to admin clients
          broadcast({ type: "esp32_connected", lockerId });
        }
        
        // Handle status updates
        if (data.type === "status") {
          const { lockerId, status, batteryLevel } = data;

          // Update locker in database and ping time
          await storage.updateLockerStatus(parseInt(lockerId), status);
          await storage.updateLockerPing(parseInt(lockerId));

          // Broadcast to admin clients
          broadcast({
            type: "esp32_status",
            lockerId,
            status,
            batteryLevel,
            timestamp: new Date().toISOString()
          });
        }
        
        // Handle command responses
        if (data.type === "command_response") {
          // Log command execution
          await storage.createActivity({
            type: "access",
            message: `ESP32 command ${data.command} executed on locker ${data.lockerId}`,
            lockerId: parseInt(data.lockerId),
            metadata: JSON.stringify({
              command: data.command,
              status: data.status,
              timestamp: new Date().toISOString()
            }),
          });
          
          // Broadcast to admin clients
          broadcast({ type: "esp32_command_executed", ...data });
        }
      } catch (error) {
        console.error("Error processing ESP32 message:", error);
      }
    });
    
    ws.on("close", () => {
      if (lockerId) {
        console.log(`ESP32 for locker ${lockerId} disconnected`);
        esp32Clients.delete(lockerId);
        
        // Update locker status
        storage.updateLockerStatus(lockerId, "disconnected")
          .catch(err => console.error("Error updating locker status:", err));
          
        // Broadcast to admin clients
        broadcast({ type: "esp32_disconnected", lockerId });
      }
    });
  });

  // Function to send command to ESP32 device
  const sendCommandToESP32 = (lockerId: number, command: string, payload?: any) => {
    const client = esp32Clients.get(lockerId);
    if (client && client.readyState === WebSocket.OPEN) {
      client.send(JSON.stringify({
        command,
        ...payload
      }));
      return true;
    }
    return false;
  };

  const clients = new Map<string, WebSocket>();

  wss.on("connection", (ws) => {
    const clientId = Math.random().toString(36).substring(7);
    clients.set(clientId, ws);

    ws.on("message", (message) => {
      try {
        const data = JSON.parse(message.toString());
        // Handle WebSocket messages
        console.log("WebSocket message:", data);
      } catch (error) {
        console.error("WebSocket message error:", error);
      }
    });

    ws.on("close", () => {
      clients.delete(clientId);
    });

    // Send initial connection confirmation
    ws.send(JSON.stringify({ type: "connected", clientId }));
  });

  // Broadcast function for real-time updates
  const broadcast = (message: any) => {
    clients.forEach((client) => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(JSON.stringify(message));
      }
    });
  };

  // Initialize Swift Agent Service with broadcast function
  swiftAgentService.setBroadcastFunction(broadcast);

  // Apply subscription middleware to relevant endpoints
  app.use('/api/users', checkMigrations);
  app.use('/api/subscription', checkMigrations);
  app.use('/api/analytics', ensureDataIntegrity);

  // Subscription migration management endpoints
  app.post("/api/admin/migrations/run", ...requireAdmin, migrationEndpoint);
  app.get("/api/admin/migrations/status", ...requireAdmin, migrationStatusEndpoint);

  // Import and register admin enhancement routes
  const systemSettingsRouter = await import("./routes/admin/system-settings");
  const auditLogsRouter = await import("./routes/admin/audit-logs");
  const alertRulesRouter = await import("./routes/admin/alert-rules");
  const scheduledReportsRouter = await import("./routes/admin/scheduled-reports");
  const reportsRouter = await import("./routes/admin/reports");
  const bulkOperationsRouter = await import("./routes/admin/bulk-operations");
  const maintenanceRouter = await import("./routes/admin/maintenance");
  const integrationsRouter = await import("./routes/admin/integrations");
  const apiMonitoringRouter = await import("./routes/admin/api-monitoring");

  app.use("/api/admin/system-settings", systemSettingsRouter.default);
  app.use("/api/admin/audit-logs", auditLogsRouter.default);
  app.use("/api/admin/alert-rules", alertRulesRouter.default);
  app.use("/api/admin/scheduled-reports", scheduledReportsRouter.default);
  app.use("/api/admin/reports", reportsRouter.default);
  app.use("/api/admin/bulk-operations", bulkOperationsRouter.default);
  app.use("/api/admin/maintenance", maintenanceRouter.default);
  app.use("/api/admin/integrations", integrationsRouter.default);
  app.use("/api/admin/api-monitoring", apiMonitoringRouter.default);

  // Statistics endpoint
  app.get("/api/stats", async (_req, res) => {
    try {
      const stats = await storage.getStats();
      res.json(stats);
    } catch (error) {
      console.error("Error fetching stats:", error);
      res.status(500).json({ message: "Failed to fetch statistics" });
    }
  });

  // Chart data endpoints
  app.get("/api/stats/utilization", async (_req, res) => {
    try {
      const lockers = await storage.getLockers();
      // const activities = await storage.getActivities(1000); // Get more activities for trends

      // Group activities by month for the last 6 months
      const now = new Date();
      const months = [];
      for (let i = 5; i >= 0; i--) {
        const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
        months.push({
          month: date.toLocaleDateString('en-US', { month: 'short' }),
          date: date,
          available: 0,
          occupied: 0,
          maintenance: 0,
          expired: 0
        });
      }

      // For now, use current locker status for all months (in real app, you'd track historical data)
      const currentStats = {
        available: lockers.filter(l => l.status === 'available').length,
        occupied: lockers.filter(l => l.status === 'occupied').length,
        maintenance: lockers.filter(l => l.status === 'maintenance').length,
        expired: lockers.filter(l => l.status === 'expired').length,
      };

      // Apply some variation to make it look more realistic
      const utilizationData = months.map((month) => ({
        month: month.month,
        available: Math.max(0, currentStats.available + Math.floor(Math.random() * 3 - 1)),
        occupied: Math.max(0, currentStats.occupied + Math.floor(Math.random() * 3 - 1)),
        maintenance: Math.max(0, currentStats.maintenance + Math.floor(Math.random() * 2)),
        expired: Math.max(0, currentStats.expired + Math.floor(Math.random() * 2)),
      }));

      res.json(utilizationData);
    } catch (error) {
      console.error("Error fetching utilization data:", error);
      res.status(500).json({ message: "Failed to fetch utilization data" });
    }
  });

  app.get("/api/stats/trends", async (_req, res) => {
    try {
      const bookings = await storage.getBookings();
      const activities = await storage.getActivities(1000);

      // Generate usage trends for the last 7 days
      const trends = [];
      const now = new Date();

      for (let i = 6; i >= 0; i--) {
        const date = new Date(now);
        date.setDate(date.getDate() - i);

        // Count bookings for this day
        const dayBookings = bookings.filter(booking => {
          if (!booking.createdAt) return false;
          const bookingDate = new Date(booking.createdAt);
          return bookingDate.toDateString() === date.toDateString();
        });

        // Count activities for this day
        const dayActivities = activities.filter(activity => {
          if (!activity.createdAt) return false;
          const activityDate = new Date(activity.createdAt);
          return activityDate.toDateString() === date.toDateString();
        });

        trends.push({
          date: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
          usage: dayBookings.length * 100 + dayActivities.length * 10, // Weight bookings more than activities
          bookings: dayBookings.length,
          activities: dayActivities.length
        });
      }

      res.json(trends);
    } catch (error) {
      console.error("Error fetching trends data:", error);
      res.status(500).json({ message: "Failed to fetch trends data" });
    }
  });

  // New analytics endpoints for real-time data
  app.get("/api/analytics/locations", async (_req, res) => {
    try {
      const lockers = await storage.getLockers();

      // Group lockers by location
      const locationStats = {};

      lockers.forEach(locker => {
        const locationName = locker.location?.name || 'Main Building';
        if (!locationStats[locationName]) {
          locationStats[locationName] = {
            location: locationName,
            total: 0,
            occupied: 0,
            available: 0,
            maintenance: 0
          };
        }

        locationStats[locationName].total++;
        if (locker.status === 'occupied') locationStats[locationName].occupied++;
        else if (locker.status === 'available') locationStats[locationName].available++;
        else if (locker.status === 'maintenance') locationStats[locationName].maintenance++;
      });

      // Calculate utilization rates
      const locationData = Object.values(locationStats).map((location: any) => ({
        ...location,
        utilization: location.total > 0 ? Math.round((location.occupied / location.total) * 100) : 0
      }));

      res.json(locationData);
    } catch (error) {
      console.error("Error fetching location analytics:", error);
      res.status(500).json({ message: "Failed to fetch location analytics" });
    }
  });

  app.get("/api/analytics/occupancy-duration", async (_req, res) => {
    try {
      const bookings = await storage.getBookings();
      const lockers = await storage.getLockers();

      // Group bookings by locker size/category
      const sizeStats = {
        'Small': { durations: [], maxDuration: 24 * 60 * 60 * 1000 }, // 24 hours
        'Medium': { durations: [], maxDuration: 48 * 60 * 60 * 1000 }, // 48 hours
        'Large': { durations: [], maxDuration: 72 * 60 * 60 * 1000 }, // 72 hours
        'Extra Large': { durations: [], maxDuration: 168 * 60 * 60 * 1000 } // 1 week
      };

      bookings.forEach(booking => {
        if (booking.endTime && booking.startTime) {
          const duration = booking.endTime - booking.startTime;
          const locker = lockers.find(l => l.id === booking.lockerId);
          const size = locker?.size || 'Medium';

          if (sizeStats[size]) {
            sizeStats[size].durations.push(duration);
          }
        }
      });

      // Calculate averages and format data
      const durationData = Object.entries(sizeStats).map(([size, data]: [string, any]) => {
        const avgDuration = data.durations.length > 0
          ? data.durations.reduce((sum: number, d: number) => sum + d, 0) / data.durations.length
          : 0;

        const formatDuration = (ms: number) => {
          const hours = Math.round(ms / (1000 * 60 * 60) * 10) / 10;
          return hours < 24 ? `${hours} hours` : `${Math.round(hours / 24 * 10) / 10} days`;
        };

        const getColorForSize = (size: string) => {
          const colors = {
            'Small': 'bg-blue-500',
            'Medium': 'bg-green-500',
            'Large': 'bg-purple-500',
            'Extra Large': 'bg-orange-500'
          };
          return colors[size] || 'bg-gray-500';
        };

        return {
          size,
          avgDuration: formatDuration(avgDuration),
          maxDuration: formatDuration(data.maxDuration),
          color: getColorForSize(size)
        };
      });

      res.json(durationData);
    } catch (error) {
      console.error("Error fetching occupancy duration analytics:", error);
      res.status(500).json({ message: "Failed to fetch occupancy duration analytics" });
    }
  });

  app.get("/api/analytics/peak-usage", async (_req, res) => {
    try {
      const bookings = await storage.getBookings();
      const activities = await storage.getActivities(1000);

      // Initialize hourly usage data
      const hourlyUsage = Array.from({ length: 24 }, (_, hour) => ({
        hour,
        bookings: 0,
        activities: 0,
        usage: 0
      }));

      // Count bookings by hour
      bookings.forEach(booking => {
        const hour = new Date(booking.createdAt).getHours();
        hourlyUsage[hour].bookings++;
      });

      // Count activities by hour
      activities.forEach(activity => {
        const hour = new Date(activity.createdAt).getHours();
        hourlyUsage[hour].activities++;
      });

      // Calculate usage percentage (normalize to 0-100)
      const maxActivity = Math.max(...hourlyUsage.map(h => h.bookings + h.activities));
      hourlyUsage.forEach(hour => {
        const totalActivity = hour.bookings + hour.activities;
        hour.usage = maxActivity > 0 ? Math.round((totalActivity / maxActivity) * 100) : 0;
      });

      res.json(hourlyUsage);
    } catch (error) {
      console.error("Error fetching peak usage analytics:", error);
      res.status(500).json({ message: "Failed to fetch peak usage analytics" });
    }
  });

  app.get("/api/analytics/agent-performance", async (_req, res) => {
    try {
      const tasks = await storage.getTasks();
      // Get all users by combining different roles
      const [allUsers, agentUsers, admins] = await Promise.all([
        storage.getUsersByRole("user"),
        storage.getUsersByRole("agent"),
        storage.getUsersByRole("admin")
      ]);
      const users = [...allUsers, ...agentUsers, ...admins];
      const bookings = await storage.getBookings();

      // Get agents only (exclude guest agent)
      const agents = users.filter(user =>
        user.role === 'agent' &&
        user.username !== 'guest-agent' &&
        user.email !== '<EMAIL>' &&
        user.id !== 'guest-agent'
      );

      const agentPerformance = agents.map(agent => {
        const agentTasks = tasks.filter(task => task.agentId === agent.id);
        const completedTasks = agentTasks.filter(task => task.status === 'completed');
        const agentBookings = bookings.filter(booking =>
          agentTasks.some(task => task.bookingId === booking.id)
        );

        // Calculate earnings (assuming $15 per completed task)
        const earnings = completedTasks.length * 15;

        // Calculate rating based on completion rate and task count
        const completionRate = agentTasks.length > 0 ? completedTasks.length / agentTasks.length : 0;
        const rating = Math.min(5.0, 3.5 + (completionRate * 1.5));

        return {
          name: `${agent.firstName || agent.username} ${agent.lastName || ''}`.trim(),
          rating: Math.round(rating * 10) / 10,
          completedTasks: completedTasks.length,
          earnings: earnings
        };
      }).sort((a, b) => b.completedTasks - a.completedTasks); // Sort by completed tasks

      res.json(agentPerformance);
    } catch (error) {
      console.error("Error fetching agent performance analytics:", error);
      res.status(500).json({ message: "Failed to fetch agent performance analytics" });
    }
  });

  app.get("/api/analytics/task-completion", async (_req, res) => {
    try {
      const tasks = await storage.getTasks();

      // Group tasks by type and calculate completion rates
      const taskTypes = {
        'Package Delivery': tasks.filter(t => t.type === 'delivery' || t.description?.includes('delivery')),
        'Locker Maintenance': tasks.filter(t => t.type === 'maintenance' || t.description?.includes('maintenance')),
        'Customer Support': tasks.filter(t => t.type === 'support' || t.description?.includes('support')),
        'Emergency Response': tasks.filter(t => t.type === 'emergency' || t.description?.includes('emergency'))
      };

      const completionData = Object.entries(taskTypes).map(([type, typeTasks]) => {
        const completed = typeTasks.filter(task => task.status === 'completed').length;
        const total = typeTasks.length;
        const rate = total > 0 ? Math.round((completed / total) * 100) : 0;

        return {
          type,
          rate,
          total,
          completed
        };
      });

      res.json(completionData);
    } catch (error) {
      console.error("Error fetching task completion analytics:", error);
      res.status(500).json({ message: "Failed to fetch task completion analytics" });
    }
  });

  // System Performance Overview endpoint
  app.get("/api/analytics/system-performance", async (_req, res) => {
    try {
      const lockers = await storage.getLockers();
      const bookings = await storage.getBookings();
      const tasks = await storage.getTasks();
      // Get all users by combining different roles
      const [allUsers, agentUsers, admins] = await Promise.all([
        storage.getUsersByRole("user"),
        storage.getUsersByRole("agent"),
        storage.getUsersByRole("admin")
      ]);
      const users = [...allUsers, ...agentUsers, ...admins];
      const activities = await storage.getActivities(100);

      const now = Date.now();
      const todayStart = new Date().setHours(0, 0, 0, 0);
      const last24Hours = now - (24 * 60 * 60 * 1000);

      // System Health Calculation
      const totalLockers = lockers.length;
      const onlineLockers = lockers.filter(l => l.lastPing && (now - l.lastPing) < 300000).length; // 5 min threshold
      const systemHealth = totalLockers > 0 ? Math.round((onlineLockers / totalLockers) * 100) : 100;

      // Active Users (users with activity in last 24 hours)
      const activeUsers = users.filter(u =>
        u.status === 'active' &&
        activities.some(a => a.userId === u.id && a.createdAt > last24Hours)
      ).length;

      // Occupancy Rate
      const occupiedLockers = lockers.filter(l => l.status === 'occupied').length;
      const occupancyRate = totalLockers > 0 ? Math.round((occupiedLockers / totalLockers) * 100) : 0;

      // Transaction Metrics
      const todayBookings = bookings.filter(b => b.createdAt >= todayStart);
      const successfulTransactions = todayBookings.filter(b => b.status === 'completed').length;
      const failedTransactions = todayBookings.filter(b => b.status === 'cancelled' || b.status === 'failed').length;
      const totalTransactions = todayBookings.length;
      const successRate = totalTransactions > 0 ? Math.round((successfulTransactions / totalTransactions) * 100) : 100;

      // Average Response Time (simulated based on system load)
      const systemLoad = occupancyRate / 100;
      const baseResponseTime = 150; // ms
      const avgResponseTime = Math.round(baseResponseTime * (1 + systemLoad * 0.5));

      // Agent Performance (exclude guest agent)
      const agents = users.filter(u =>
        u.role === 'agent' &&
        u.username !== 'guest-agent' &&
        u.email !== '<EMAIL>' &&
        u.id !== 'guest-agent'
      );
      const activeAgents = agents.filter(a => a.status === 'active').length;
      const todayTasks = tasks.filter(t => t.createdAt >= todayStart);
      const completedTasks = todayTasks.filter(t => t.status === 'completed').length;
      const taskCompletionRate = todayTasks.length > 0 ? Math.round((completedTasks / todayTasks.length) * 100) : 100;

      // Revenue Calculation
      const todayRevenue = successfulTransactions * 5 + completedTasks * 15; // $5 per booking, $15 per task
      const revenueTarget = 500; // Daily target
      const revenueProgress = Math.min(Math.round((todayRevenue / revenueTarget) * 100), 100);

      // Hardware Status
      const maintenanceLockers = lockers.filter(l => l.status === 'maintenance').length;
      const offlineLockers = totalLockers - onlineLockers;
      const hardwareHealthRate = totalLockers > 0 ? Math.round(((totalLockers - maintenanceLockers - offlineLockers) / totalLockers) * 100) : 100;

      // System Uptime (simulated - in real system would track actual uptime)
      const uptime = 99.9;

      // Peak Usage Hours (last 24 hours)
      const hourlyUsage = Array.from({ length: 24 }, (_, hour) => ({
        hour,
        usage: 0
      }));

      todayBookings.forEach(booking => {
        const hour = new Date(booking.createdAt).getHours();
        hourlyUsage[hour].usage++;
      });

      const peakHour = hourlyUsage.reduce((max, current) =>
        current.usage > max.usage ? current : max
      );

      res.json({
        systemHealth: {
          status: systemHealth >= 95 ? 'excellent' : systemHealth >= 85 ? 'good' : systemHealth >= 70 ? 'warning' : 'critical',
          percentage: systemHealth,
          uptime
        },
        realTimeMetrics: {
          activeUsers,
          occupancyRate,
          avgResponseTime,
          onlineLockers,
          totalLockers
        },
        transactionMetrics: {
          successfulTransactions,
          failedTransactions,
          totalTransactions,
          successRate
        },
        agentPerformance: {
          activeAgents,
          totalAgents: agents.length,
          taskCompletionRate,
          avgResponseTime: Math.round(Math.random() * 300 + 120) // Simulated agent response time
        },
        revenue: {
          todayRevenue,
          revenueTarget,
          revenueProgress
        },
        hardwareStatus: {
          onlineLockers,
          offlineLockers,
          maintenanceLockers,
          healthRate: hardwareHealthRate
        },
        peakUsage: {
          currentHour: new Date().getHours(),
          peakHour: peakHour.hour,
          peakUsage: peakHour.usage,
          hourlyData: hourlyUsage
        }
      });

    } catch (error) {
      console.error("Error fetching system performance:", error);
      res.status(500).json({ message: "Failed to fetch system performance data" });
    }
  });

  // Enhanced Overall Analytics endpoint
  app.get("/api/analytics/overview-enhanced", async (_req, res) => {
    try {
      const lockers = await storage.getLockers();
      const bookings = await storage.getBookings();
      const tasks = await storage.getTasks();
      // Get all users by combining different roles
      const [allUsers, agentUsers, admins] = await Promise.all([
        storage.getUsersByRole("user"),
        storage.getUsersByRole("agent"),
        storage.getUsersByRole("admin")
      ]);
      const users = [...allUsers, ...agentUsers, ...admins];
      const activities = await storage.getActivities(50);
      const locations = await storage.getLocations();

      const now = Date.now();
      const todayStart = new Date().setHours(0, 0, 0, 0);
      const yesterdayStart = todayStart - (24 * 60 * 60 * 1000);
      const weekStart = todayStart - (7 * 24 * 60 * 60 * 1000);
      const monthStart = todayStart - (30 * 24 * 60 * 60 * 1000);

      // Enhanced KPIs with trends
      const todayBookings = bookings.filter(b => b.createdAt >= todayStart);
      const yesterdayBookings = bookings.filter(b => b.createdAt >= yesterdayStart && b.createdAt < todayStart);
      const weekBookings = bookings.filter(b => b.createdAt >= weekStart);
      const monthBookings = bookings.filter(b => b.createdAt >= monthStart);

      const todayTasks = tasks.filter(t => t.createdAt >= todayStart);
      const yesterdayTasks = tasks.filter(t => t.createdAt >= yesterdayStart && t.createdAt < todayStart);

      const activeUsers = users.filter(u => u.status === 'active').length;
      const totalUsers = users.length;
      const newUsersToday = users.filter(u => u.createdAt >= todayStart).length;
      const newUsersYesterday = users.filter(u => u.createdAt >= yesterdayStart && u.createdAt < todayStart).length;

      // Revenue calculations
      const todayRevenue = todayBookings.filter(b => b.status === 'completed').length * 5 +
                          todayTasks.filter(t => t.status === 'completed').length * 15;
      const yesterdayRevenue = yesterdayBookings.filter(b => b.status === 'completed').length * 5 +
                              yesterdayTasks.filter(t => t.status === 'completed').length * 15;
      const weekRevenue = weekBookings.filter(b => b.status === 'completed').length * 5 +
                         tasks.filter(t => t.status === 'completed' && t.createdAt >= weekStart).length * 15;

      // Utilization metrics
      const occupiedLockers = lockers.filter(l => l.status === 'occupied').length;
      const totalLockers = lockers.length;
      const utilizationRate = totalLockers > 0 ? Math.round((occupiedLockers / totalLockers) * 100) : 0;

      // Success rates
      const completedBookingsToday = todayBookings.filter(b => b.status === 'completed').length;
      const totalBookingsToday = todayBookings.length;
      const successRateToday = totalBookingsToday > 0 ? Math.round((completedBookingsToday / totalBookingsToday) * 100) : 100;

      const completedTasksToday = todayTasks.filter(t => t.status === 'completed').length;
      const totalTasksToday = todayTasks.length;
      const taskSuccessRate = totalTasksToday > 0 ? Math.round((completedTasksToday / totalTasksToday) * 100) : 100;

      // Trend calculations
      const bookingTrend = yesterdayBookings.length > 0 ?
        Math.round(((todayBookings.length - yesterdayBookings.length) / yesterdayBookings.length) * 100) :
        (todayBookings.length > 0 ? 100 : 0);

      const revenueTrend = yesterdayRevenue > 0 ?
        Math.round(((todayRevenue - yesterdayRevenue) / yesterdayRevenue) * 100) :
        (todayRevenue > 0 ? 100 : 0);

      const userTrend = newUsersYesterday > 0 ?
        Math.round(((newUsersToday - newUsersYesterday) / newUsersYesterday) * 100) :
        (newUsersToday > 0 ? 100 : 0);

      // Recent activities (last 10)
      const recentActivities = activities.slice(0, 10).map(activity => ({
        id: activity.id,
        type: activity.type,
        message: activity.message,
        timestamp: activity.createdAt,
        userId: activity.userId,
        timeAgo: getTimeAgo(activity.createdAt)
      }));

      // Location performance
      const locationPerformance = locations.map(location => {
        const locationLockers = lockers.filter(l => l.locationId === location.id);
        const locationBookings = bookings.filter(b =>
          locationLockers.some(l => l.id === b.lockerId) && b.createdAt >= weekStart
        );
        const locationRevenue = locationBookings.filter(b => b.status === 'completed').length * 5;

        return {
          id: location.id,
          name: location.name,
          totalLockers: locationLockers.length,
          occupiedLockers: locationLockers.filter(l => l.status === 'occupied').length,
          weeklyBookings: locationBookings.length,
          weeklyRevenue: locationRevenue,
          utilizationRate: locationLockers.length > 0 ?
            Math.round((locationLockers.filter(l => l.status === 'occupied').length / locationLockers.length) * 100) : 0
        };
      });

      // Hourly usage pattern (last 24 hours)
      const hourlyUsage = Array.from({ length: 24 }, (_, hour) => {
        const hourStart = todayStart + (hour * 60 * 60 * 1000);
        const hourEnd = hourStart + (60 * 60 * 1000);
        const hourBookings = bookings.filter(b => b.createdAt >= hourStart && b.createdAt < hourEnd);
        const hourActivities = activities.filter(a => a.createdAt >= hourStart && a.createdAt < hourEnd);

        return {
          hour,
          bookings: hourBookings.length,
          activities: hourActivities.length,
          usage: Math.min(100, (hourBookings.length + hourActivities.length) * 10)
        };
      });

      // Weekly comparison
      const weeklyComparison = Array.from({ length: 7 }, (_, dayOffset) => {
        const dayStart = todayStart - (dayOffset * 24 * 60 * 60 * 1000);
        const dayEnd = dayStart + (24 * 60 * 60 * 1000);
        const dayBookings = bookings.filter(b => b.createdAt >= dayStart && b.createdAt < dayEnd);
        const dayTasks = tasks.filter(t => t.createdAt >= dayStart && t.createdAt < dayEnd);
        const dayRevenue = dayBookings.filter(b => b.status === 'completed').length * 5 +
                          dayTasks.filter(t => t.status === 'completed').length * 15;

        return {
          day: new Date(dayStart).toLocaleDateString('en-US', { weekday: 'short' }),
          date: new Date(dayStart).toLocaleDateString(),
          bookings: dayBookings.length,
          tasks: dayTasks.length,
          revenue: dayRevenue,
          isToday: dayOffset === 0
        };
      }).reverse();

      res.json({
        kpis: {
          totalBookings: {
            value: todayBookings.length,
            trend: bookingTrend,
            previousValue: yesterdayBookings.length,
            weekTotal: weekBookings.length,
            monthTotal: monthBookings.length
          },
          totalRevenue: {
            value: todayRevenue,
            trend: revenueTrend,
            previousValue: yesterdayRevenue,
            weekTotal: weekRevenue,
            target: 500 // Daily target
          },
          activeUsers: {
            value: activeUsers,
            total: totalUsers,
            newToday: newUsersToday,
            trend: userTrend,
            previousValue: newUsersYesterday
          },
          utilizationRate: {
            value: utilizationRate,
            occupiedLockers,
            totalLockers,
            trend: 0 // Could calculate based on historical data
          },
          successRate: {
            bookings: successRateToday,
            tasks: taskSuccessRate,
            overall: Math.round((successRateToday + taskSuccessRate) / 2)
          }
        },
        recentActivities,
        locationPerformance,
        hourlyUsage,
        weeklyComparison,
        insights: {
          peakHour: hourlyUsage.reduce((max, current) => current.usage > max.usage ? current : max),
          topLocation: locationPerformance.reduce((max, current) =>
            current.weeklyRevenue > max.weeklyRevenue ? current : max, locationPerformance[0] || {}),
          growthRate: bookingTrend,
          efficiency: Math.round((completedBookingsToday + completedTasksToday) / Math.max(totalBookingsToday + totalTasksToday, 1) * 100)
        }
      });

    } catch (error) {
      console.error("Error fetching enhanced overview:", error);
      res.status(500).json({ message: "Failed to fetch enhanced overview data" });
    }
  });

  // Helper function for time ago calculation
  function getTimeAgo(timestamp: number): string {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  }

  app.get("/api/analytics/revenue", async (_req, res) => {
    try {
      const bookings = await storage.getBookings();
      const tasks = await storage.getTasks();

      // Calculate revenue from bookings (assuming $5 per booking)
      const bookingRevenue = bookings.filter(b => b.status === 'completed').length * 5;

      // Calculate revenue from agent tasks (assuming $15 per completed task)
      const taskRevenue = tasks.filter(t => t.status === 'completed').length * 15;

      const totalRevenue = bookingRevenue + taskRevenue;

      // Generate monthly revenue for last 6 months
      const monthlyRevenue = [];
      const now = new Date();

      for (let i = 5; i >= 0; i--) {
        const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
        const nextMonth = new Date(now.getFullYear(), now.getMonth() - i + 1, 1);

        const monthBookings = bookings.filter(booking => {
          const bookingDate = new Date(booking.createdAt);
          return bookingDate >= date && bookingDate < nextMonth && booking.status === 'completed';
        });

        const monthTasks = tasks.filter(task => {
          const taskDate = new Date(task.createdAt);
          return taskDate >= date && taskDate < nextMonth && task.status === 'completed';
        });

        monthlyRevenue.push({
          month: date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
          bookingRevenue: monthBookings.length * 5,
          taskRevenue: monthTasks.length * 15,
          total: (monthBookings.length * 5) + (monthTasks.length * 15)
        });
      }

      res.json({
        totalRevenue,
        bookingRevenue,
        taskRevenue,
        monthlyRevenue,
        breakdown: {
          'Locker Rentals': bookingRevenue,
          'Agent Services': taskRevenue,
          'Maintenance': Math.round(totalRevenue * 0.1), // 10% for maintenance
          'Other': Math.round(totalRevenue * 0.05) // 5% other
        }
      });
    } catch (error) {
      console.error("Error fetching revenue analytics:", error);
      res.status(500).json({ message: "Failed to fetch revenue analytics" });
    }
  });

  // Locker endpoints
  app.get("/api/lockers", async (_req, res) => {
    try {
      const lockers = await storage.getLockers();
      res.json(lockers);
    } catch (error) {
      console.error("Error fetching lockers:", error);
      res.status(500).json({ message: "Failed to fetch lockers" });
    }
  });

  app.post("/api/lockers", async (req, res) => {
    try {
      const lockerData = insertLockerSchema.parse(req.body);
      const locker = await storage.createLocker(lockerData);
      
      // Log activity
      await storage.createActivity({
        type: "maintenance",
        message: `Locker ${locker.code} created`,
        lockerId: locker.id,
      });

      broadcast({ type: "locker_created", locker });
      res.json(locker);
    } catch (error) {
      console.error("Error creating locker:", error);
      res.status(500).json({ message: "Failed to create locker" });
    }
  });

  app.patch("/api/lockers/:id/status", async (req, res) => {
    try {
      const lockerId = parseInt(req.params.id);
      const { status } = req.body;

      const locker = await storage.updateLockerStatus(lockerId, status);
      if (!locker) {
        return res.status(404).json({ message: "Locker not found" });
      }

      // Log activity
      await storage.createActivity({
        type: "access",
        message: `Locker ${locker.code} status changed to ${status}`,
        lockerId: locker.id,
      });

      broadcast({ type: "locker_updated", locker });
      res.json(locker);
    } catch (error) {
      console.error("Error updating locker status:", error);
      res.status(500).json({ message: "Failed to update locker status" });
    }
  });

  // Delete locker endpoint (admin only)
  app.delete("/api/lockers/:id", async (req, res) => {
    try {
      const lockerId = parseInt(req.params.id);

      // Get locker details before deletion for logging
      const locker = await storage.getLocker(lockerId);
      if (!locker) {
        return res.status(404).json({ message: "Locker not found" });
      }

      // Check if locker can be safely deleted
      const deleted = await storage.deleteLocker(lockerId);

      if (!deleted) {
        return res.status(500).json({ message: "Failed to delete locker" });
      }

      // Log activity
      await storage.createActivity({
        type: "maintenance",
        message: `Locker ${locker.code} deleted by admin`,
        lockerId: locker.id,
      });

      // Broadcast deletion event
      broadcast({
        type: "locker_deleted",
        lockerId: locker.id,
        lockerCode: locker.code
      });

      res.json({
        success: true,
        message: `Locker ${locker.code} has been successfully deleted`
      });
    } catch (error) {
      console.error("Error deleting locker:", error);

      if (error instanceof Error && error.message.includes("active bookings")) {
        return res.status(400).json({
          message: "Cannot delete locker with active bookings. Please wait for bookings to complete or cancel them first.",
          error: "ACTIVE_BOOKINGS_EXIST"
        });
      }

      res.status(500).json({ message: "Failed to delete locker" });
    }
  });

  // Initialize Baguio City locations if none exist
  app.post("/api/locations/init-baguio", async (_req, res) => {
    try {
      const existingLocations = await storage.getLocations();

      if (existingLocations.length === 0) {
        const baguioLocations = [
          {
            name: "SM City Baguio",
            address: "Upper Session Rd, Baguio, Benguet",
            city: "Baguio",
            state: "Benguet",
            zipCode: "2600",
            country: "Philippines",
            latitude: 16.4108,
            longitude: 120.5987,
            capacity: 20,
          },
          {
            name: "Session Road",
            address: "Session Road, Baguio City Center",
            city: "Baguio",
            state: "Benguet",
            zipCode: "2600",
            country: "Philippines",
            latitude: 16.4095,
            longitude: 120.5930,
            capacity: 15,
          },
          {
            name: "Burnham Park",
            address: "Jose Abad Santos Dr, Baguio, Benguet",
            city: "Baguio",
            state: "Benguet",
            zipCode: "2600",
            country: "Philippines",
            latitude: 16.4062,
            longitude: 120.5922,
            capacity: 12,
          },
          {
            name: "University of the Philippines Baguio",
            address: "Governor Pack Road, Baguio, Benguet",
            city: "Baguio",
            state: "Benguet",
            zipCode: "2600",
            country: "Philippines",
            latitude: 16.3988,
            longitude: 120.5916,
            capacity: 25,
          },
          {
            name: "Baguio Cathedral",
            address: "Cathedral Loop, Baguio, Benguet",
            city: "Baguio",
            state: "Benguet",
            zipCode: "2600",
            country: "Philippines",
            latitude: 16.4137,
            longitude: 120.5926,
            capacity: 10,
          }
        ];

        for (const locationData of baguioLocations) {
          await storage.createLocation(locationData);
        }

        res.json({ message: "Baguio City locations initialized successfully", count: baguioLocations.length });
      } else {
        res.json({ message: "Locations already exist", count: existingLocations.length });
      }
    } catch (error) {
      console.error("Error initializing Baguio locations:", error);
      res.status(500).json({ message: "Failed to initialize locations" });
    }
  });

  // Initialize sample lockers for Baguio locations
  app.post("/api/lockers/init-baguio", async (_req, res) => {
    try {
      const locations = await storage.getLocations();
      const existingLockers = await storage.getLockers();

      if (existingLockers.length === 0 && locations.length > 0) {
        const lockerSizes = ['Small', 'Medium', 'Large'];
        const lockerStatuses = ['available', 'occupied', 'maintenance'];
        let lockerCount = 0;

        for (const location of locations) {
          const numLockers = Math.min(location.capacity || 10, 15); // Max 15 lockers per location

          for (let i = 1; i <= numLockers; i++) {
            const size = lockerSizes[Math.floor(Math.random() * lockerSizes.length)];
            const status = Math.random() > 0.3 ? 'available' : lockerStatuses[Math.floor(Math.random() * lockerStatuses.length)];
            const code = `${location.name.substring(0, 2).toUpperCase()}${String(i).padStart(3, '0')}`;

            await storage.createLocker({
              code,
              size,
              status,
              locationId: location.id,
              esp32Id: `esp32_${location.id}_${i}`,
              lastMaintenanceDate: Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000, // Random date within last 30 days
              maintenanceInterval: 90, // 90 days
              temperatureSensor: Math.random() > 0.5,
              humiditySensor: Math.random() > 0.7,
            });

            lockerCount++;
          }
        }

        res.json({ message: "Baguio City lockers initialized successfully", count: lockerCount });
      } else {
        res.json({ message: "Lockers already exist or no locations found", count: existingLockers.length });
      }
    } catch (error) {
      console.error("Error initializing Baguio lockers:", error);
      res.status(500).json({ message: "Failed to initialize lockers" });
    }
  });

  // Location endpoints
  app.get("/api/locations", async (_req, res) => {
    try {
      const locations = await storage.getLocations();
      res.json(locations);
    } catch (error) {
      console.error("Error fetching locations:", error);
      res.status(500).json({ message: "Failed to fetch locations" });
    }
  });

  // Booking endpoints
  app.get("/api/bookings", async (_req, res) => {
    try {
      const bookings = await storage.getBookings();
      res.json(bookings);
    } catch (error) {
      console.error("Error fetching bookings:", error);
      res.status(500).json({ message: "Failed to fetch bookings" });
    }
  });

  // Get bookings for a specific user with access session information
  app.get("/api/bookings/user/:userId", async (req, res) => {
    try {
      const { userId } = req.params;
      const bookings = await storage.getBookingsByUser(userId);

      // Enhance bookings with access session information
      const enhancedBookings = await Promise.all(
        bookings.map(async (booking) => {
          // Get access sessions for this booking
          const userSessions = await storage.getAccessSessionsByUser(userId);
          const bookingSession = userSessions.find(session =>
            session.bookingId === booking.id &&
            (session.status === 'active' || session.status === 'grace_period' || session.status === 'expired')
          );

          // Debug logging (can be removed in production)
          if (process.env.NODE_ENV === 'development') {
            console.log(`Booking ${booking.id}: Found ${userSessions.length} sessions for user ${userId}`);
            if (bookingSession) {
              console.log(`Booking ${booking.id}: Found matching session ${bookingSession.id} with status ${bookingSession.status}, graceEnd: ${bookingSession.graceEnd}`);
            } else {
              console.log(`Booking ${booking.id}: No matching session found`);
              console.log(`Available sessions:`, userSessions.map(s => ({ id: s.id, bookingId: s.bookingId, status: s.status })));
            }
          }

          return {
            ...booking,
            accessSession: bookingSession || null
          };
        })
      );

      res.json(enhancedBookings);
    } catch (error) {
      console.error("Error fetching user bookings:", error);
      res.status(500).json({ message: "Failed to fetch user bookings" });
    }
  });

  app.post("/api/bookings", async (req, res) => {
    try {
      const bookingData = insertBookingSchema.parse(req.body);
      const booking = await storage.createBooking(bookingData);

      // Update locker status to occupied
      await storage.updateLockerStatus(booking.lockerId, "occupied");

      // Create access session for the booking
      const now = Date.now();
      const sessionEnd = booking.endTime || (now + (booking.duration || 120) * 60 * 1000); // duration is in minutes, convert to milliseconds
      const graceStart = sessionEnd;
      const graceEnd = graceStart + (20 * 60 * 1000); // Default 20 minutes grace period

      const accessSessionData = {
        bookingId: booking.id,
        userId: booking.userId,
        agentId: null, // No agent for direct user bookings
        lockerId: booking.lockerId,
        accessMethod: 'booking', // Access method for regular bookings
        sessionStart: booking.startTime,
        sessionEnd,
        graceStart,
        graceEnd,
        status: 'active' as const,
        warningsSent: 0,
        metadata: JSON.stringify({
          grantedBy: 'booking_system',
          bookingId: booking.id,
          timestamp: new Date().toISOString()
        })
      };

      try {
        const accessSession = await storage.createLockerAccessSession(accessSessionData);
        console.log(`✅ Created access session ${accessSession.id} for booking ${booking.id}`);
      } catch (sessionError) {
        console.error(`❌ Failed to create access session for booking ${booking.id}:`, sessionError);
        // Don't fail the booking creation if access session fails
      }

      // Automatically unlock the locker for the user
      try {
        const unlockSuccess = sendCommandToESP32(booking.lockerId, "unlock", {
          userId: booking.userId,
          duration: booking.duration || 120,
          bookingId: booking.id
        });

        if (unlockSuccess) {
          console.log(`✅ Unlock command sent to locker ${booking.lockerId} for booking ${booking.id}`);

          // Log unlock activity
          await storage.createActivity({
            type: "access",
            message: `Locker ${booking.lockerId} automatically unlocked for new booking`,
            lockerId: booking.lockerId,
            userId: booking.userId,
            metadata: JSON.stringify({
              bookingId: booking.id,
              autoUnlock: true,
              timestamp: new Date().toISOString()
            }),
          });
        } else {
          console.warn(`⚠️ Failed to send unlock command to locker ${booking.lockerId} - device may be offline`);
        }
      } catch (unlockError) {
        console.error(`❌ Error unlocking locker ${booking.lockerId}:`, unlockError);
        // Don't fail the booking creation if unlock fails
      }

      // Log activity
      await storage.createActivity({
        type: "booking",
        message: `Booking created for locker ${booking.lockerId}`,
        lockerId: booking.lockerId,
        userId: booking.userId,
      });

      // Send admin notification for locker rental
      const notificationData = {
        type: 'admin_notification',
        notificationType: 'locker_rental',
        title: 'New Locker Rental',
        message: `User has rented locker #${booking.lockerId} for ${booking.duration || 0} hours`,
        lockerId: booking.lockerId.toString(),
        lockerCode: `L${booking.lockerId.toString().padStart(3, '0')}`,
        userId: booking.userId,
        userName: `User ${booking.userId?.slice(-6) || 'Unknown'}`,
        priority: 'high',
        timestamp: Date.now()
      };

      broadcast({ type: "booking_created", booking });
      broadcast(notificationData);
      res.json(booking);
    } catch (error) {
      console.error("Error creating booking:", error);
      res.status(500).json({ message: "Failed to create booking" });
    }
  });

  app.patch("/api/bookings/:id/status", async (req, res) => {
    try {
      const bookingId = parseInt(req.params.id);
      const { status } = req.body;
      
      const booking = await storage.updateBookingStatus(bookingId, status);
      if (!booking) {
        return res.status(404).json({ message: "Booking not found" });
      }

      // If booking is completed, make locker available
      if (status === "completed") {
        await storage.updateLockerStatus(booking.lockerId, "available");
      }

      broadcast({ type: "booking_updated", booking });
      res.json(booking);
    } catch (error) {
      console.error("Error updating booking status:", error);
      res.status(500).json({ message: "Failed to update booking status" });
    }
  });

  // Task endpoints
  app.get("/api/tasks", async (req, res) => {
    try {
      const { agentId } = req.query;
      const tasks = agentId 
        ? await storage.getTasksByAgent(agentId as string)
        : await storage.getTasks();
      res.json(tasks);
    } catch (error) {
      console.error("Error fetching tasks:", error);
      res.status(500).json({ message: "Failed to fetch tasks" });
    }
  });

  app.post("/api/tasks", async (req, res) => {
    try {
      console.log("Received task creation request:", req.body);
      const taskData = insertTaskSchema.parse(req.body);
      console.log("Parsed task data:", taskData);

      const task = await storage.createTask(taskData);
      console.log("Created task:", task);

      broadcast({ type: "task_created", task });
      res.json(task);
    } catch (error) {
      console.error("Error creating task:", error);
      console.error("Request body:", req.body);
      res.status(500).json({ message: "Failed to create task" });
    }
  });

  app.patch("/api/tasks/:id/status", async (req, res) => {
    try {
      const taskId = parseInt(req.params.id);
      const { status } = req.body;
      
      const task = await storage.updateTaskStatus(taskId, status);
      if (!task) {
        return res.status(404).json({ message: "Task not found" });
      }

      broadcast({ type: "task_updated", task });
      res.json(task);
    } catch (error) {
      console.error("Error updating task status:", error);
      res.status(500).json({ message: "Failed to update task status" });
    }
  });

  // Activity endpoints
  app.get("/api/activities", async (req, res) => {
    try {
      const { limit } = req.query;
      const activities = await storage.getActivities(limit ? parseInt(limit as string) : undefined);
      res.json(activities);
    } catch (error) {
      console.error("Error fetching activities:", error);
      res.status(500).json({ message: "Failed to fetch activities" });
    }
  });

  // User endpoints - using real database storage
  app.get("/api/users", async (req, res) => {
    try {
      const { role } = req.query;

      console.log(`Fetching users with role: ${role || 'all'}`);

      let users;
      if (role) {
        const allUsersOfRole = await storage.getUsersByRole(role as string);
        // Filter out guest users
        users = allUsersOfRole.filter(user =>
          user.username !== 'guest-agent' &&
          user.username !== 'guest-user' &&
          user.email !== '<EMAIL>' &&
          user.email !== '<EMAIL>' &&
          user.id !== 'guest-agent' &&
          user.id !== 'guest-user'
        );
      } else {
        // If no role specified, get all users
        const [allUsers, agents, admins] = await Promise.all([
          storage.getUsersByRole("user"),
          storage.getUsersByRole("agent"),
          storage.getUsersByRole("admin")
        ]);
        const allCombined = [...allUsers, ...agents, ...admins];
        // Filter out guest users
        users = allCombined.filter(user =>
          user.username !== 'guest-agent' &&
          user.username !== 'guest-user' &&
          user.email !== '<EMAIL>' &&
          user.email !== '<EMAIL>' &&
          user.id !== 'guest-agent' &&
          user.id !== 'guest-user'
        );
      }

      console.log(`Found ${users.length} users`);
      res.json(users);
    } catch (error) {
      console.error("Error fetching users:", error);

      // Return error if database is not available
      console.log("Database unavailable, returning error");
      res.status(500).json({ message: "Database connection failed" });
    }
  });

  // Test endpoint for debugging
  app.get("/api/test/connection", async (_req, res) => {
    try {
      const agents = await storage.getUsersByRole("agent");
      res.json({
        status: "connected",
        timestamp: new Date().toISOString(),
        agentCount: agents.length,
        agents: agents.map(a => ({ id: a.id, username: a.username, status: a.status }))
      });
    } catch (error) {
      res.status(500).json({
        status: "error",
        message: error instanceof Error ? error.message : "Unknown error"
      });
    }
  });

  // Authentication endpoints
  app.post("/api/auth/login",
    bruteForceProtection,
    accountLockoutProtection,
    validationSchemas.login,
    handleValidationErrors,
    async (req: Request, res: Response) => {
    try {
      const { username, password, role } = req.body;

      console.log(`Login attempt - Username: ${username}, Role: ${role || 'any'}`);

      // Validate required fields (already handled by validation middleware)
      if (!username || !password) {
        return res.status(400).json({ message: "Username and password are required" });
      }

      // Get users by role or all users if no role specified
      let users;
      if (role) {
        users = await storage.getUsersByRole(role);
      } else {
        // Get all users from all roles
        const [regularUsers, agents, admins] = await Promise.all([
          storage.getUsersByRole("user"),
          storage.getUsersByRole("agent"),
          storage.getUsersByRole("admin")
        ]);
        users = [...regularUsers, ...agents, ...admins];
      }

      // Find user by username or email
      const user = users.find(u =>
        u.username === username || u.email === username
      );

      if (!user) {
        console.log(`User not found: ${username}`);
        return res.status(401).json({ message: "Invalid credentials" });
      }

      // Check password using bcrypt
      if (!user.password) {
        console.log(`No password set for user: ${username}`);
        return res.status(401).json({ message: "Invalid credentials" });
      }

      const isValidPassword = await passwordUtils.verify(password, user.password);
      if (!isValidPassword) {
        console.log(`Invalid password for user: ${username}`);

        // Record failed login attempt
        if (res.locals.recordFailedLogin) {
          res.locals.recordFailedLogin();
        }

        // Log security event
        await securityLogger.logEvent(createSecurityEvent(
          req as any,
          SecurityEventType.LOGIN_FAILURE,
          'medium',
          `Failed login attempt for user: ${username}`,
          { username, role }
        ));

        return res.status(401).json({ message: "Invalid credentials" });
      }

      // Check if user is active
      if (user.status !== "active") {
        console.log(`User account not active: ${username}, status: ${user.status}`);

        // Handle different account statuses with specific messages
        if (user.status === "restricted") {
          const restrictionReason = user.restrictionReason || "Account has been restricted by an administrator";
          return res.status(403).json({
            message: "Account Restricted",
            reason: restrictionReason,
            status: "restricted",
            contactSupport: true
          });
        } else if (user.status === "suspended") {
          return res.status(403).json({
            message: "Account Suspended",
            reason: "Your account has been suspended. Please contact support for assistance.",
            status: "suspended",
            contactSupport: true
          });
        } else {
          return res.status(401).json({ message: "Account is not active" });
        }
      }

      // Log successful login
      await storage.createActivity({
        type: "authentication",
        message: `User ${user.firstName} ${user.lastName} logged in`,
        userId: user.id,
        metadata: JSON.stringify({
          loginTime: new Date().toISOString(),
          role: user.role,
          method: "password"
        }),
      });

      console.log(`Login successful for user: ${username}, role: ${user.role}`);

      // Record successful login
      if (res.locals.recordSuccessfulLogin) {
        res.locals.recordSuccessfulLogin();
      }

      // Generate JWT token
      const token = jwtUtils.sign({
        userId: user.id,
        username: user.username,
        role: user.role
      });

      // Store token in session
      req.session.token = token;
      req.session.userId = user.id;

      // Log successful login
      await securityLogger.logEvent(createSecurityEvent(
        req as any,
        SecurityEventType.LOGIN_SUCCESS,
        'low',
        `Successful login for user: ${username}`,
        { username, role: user.role }
      ));

      // Return user data (excluding password) with token
      const { password: _, ...userWithoutPassword } = user;
      res.json({
        ...userWithoutPassword,
        token
      });
    } catch (error) {
      console.error("Login error:", error);
      res.status(500).json({ message: "Login failed" });
    }
  });

  app.post("/api/auth/register",
    validationSchemas.createUser,
    handleValidationErrors,
    async (req: Request, res: Response) => {
    try {
      const userData = req.body;

      // Validate required fields (already handled by validation middleware)
      if (!userData.email || !userData.username || !userData.password) {
        return res.status(400).json({ message: "Email, username, and password are required" });
      }

      // Check if user already exists by email or username
      const allUsers = await storage.getUsersByRole("user");
      const existingUser = allUsers.find(user =>
        user.email === userData.email || user.username === userData.username
      );

      if (existingUser) {
        const field = existingUser.email === userData.email ? "email" : "username";
        return res.status(400).json({ message: `User with this ${field} already exists` });
      }

      // Generate unique ID for the user
      const userId = `user_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;

      // Hash password before storing
      const hashedPassword = await passwordUtils.hash(userData.password);

      const newUser = await storage.upsertUser({
        id: userId,
        email: userData.email,
        firstName: userData.firstName || null,
        lastName: userData.lastName || null,
        phoneNumber: userData.phoneNumber || null,
        username: userData.username,
        password: hashedPassword,
        profileImageUrl: null,
        role: userData.role || "user",
        status: "active",
        verificationStatus: false,
      });

      // Automatically initialize subscription for new users
      try {
        await subscriptionMigrator.initializeUserSubscription(userId, 'basic');
        console.log(`✅ Subscription initialized for new user: ${userId}`);
      } catch (subscriptionError) {
        console.warn(`⚠️ Failed to initialize subscription for user ${userId}:`, subscriptionError);
        // Don't fail the registration if subscription initialization fails
      }

      // Log registration activity
      await storage.createActivity({
        type: "registration",
        message: `New user registered: ${newUser.firstName} ${newUser.lastName}`,
        userId: newUser.id,
      });

      console.log("User registered successfully:", newUser.username);

      // Return user data (excluding password)
      const { password: _, ...userWithoutPassword } = newUser;
      res.json(userWithoutPassword);
    } catch (error) {
      console.error("Registration error:", error);
      res.status(500).json({ message: "Registration failed" });
    }
  });

  // User registration endpoint
  app.post("/api/users", async (req, res) => {
    try {
      const userData = req.body;

      // Validate required fields
      if (!userData.email) {
        return res.status(400).json({ message: "Email is required" });
      }

      // Check if user already exists by email
      const allUsers = await storage.getUsersByRole("user");
      const existingUser = allUsers.find(user => user.email === userData.email);
      if (existingUser) {
        return res.status(400).json({ message: "User with this email already exists" });
      }

      // Generate unique ID for the user
      const userId = `user_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;

      const newUser = await storage.upsertUser({
        id: userId,
        email: userData.email,
        firstName: userData.firstName || null,
        lastName: userData.lastName || null,
        phoneNumber: userData.phoneNumber || null,
        username: userData.username || null,
        password: userData.password || null,
        profileImageUrl: null,
        role: "user",
        status: "active",
        verificationStatus: false,
        // Note: createdAt and updatedAt are omitted as they're auto-generated
      });

      // Automatically initialize subscription for new users
      try {
        await subscriptionMigrator.initializeUserSubscription(userId, 'basic');
        console.log(`✅ Subscription initialized for new user: ${userId}`);
      } catch (subscriptionError) {
        console.warn(`⚠️ Failed to initialize subscription for user ${userId}:`, subscriptionError);
        // Don't fail the registration if subscription initialization fails
      }

      // Log registration activity
      await storage.createActivity({
        type: "registration",
        message: `New user registered: ${newUser.firstName} ${newUser.lastName}`,
        userId: newUser.id,
      });

      broadcast({ type: "user_registered", user: newUser });
      res.status(201).json(newUser);
    } catch (error) {
      console.error("Error creating user:", error);
      res.status(500).json({ message: "Failed to create user" });
    }
  });

  // Agent/Admin creation endpoint
  app.post("/api/agents",
    ...requireAdmin,
    validationSchemas.createUser,
    handleValidationErrors,
    async (req: Request, res: Response) => {
    const userData = req.body;
    const userRole = userData.role || "agent"; // Default to agent if no role specified

    try {
      console.log(`Creating new ${userRole} with data:`, userData);

      // Validate required fields
      if (!userData.email) {
        return res.status(400).json({ message: "Email is required" });
      }
      if (!userData.firstName) {
        return res.status(400).json({ message: "First name is required" });
      }
      if (!userData.lastName) {
        return res.status(400).json({ message: "Last name is required" });
      }
      if (!userData.username) {
        return res.status(400).json({ message: "Username is required" });
      }
      if (!userData.password) {
        return res.status(400).json({ message: "Password is required" });
      }

      // Check if user with this email already exists across all roles
      const [users, agents, admins] = await Promise.all([
        storage.getUsersByRole("user"),
        storage.getUsersByRole("agent"),
        storage.getUsersByRole("admin")
      ]);

      const allUsers = [...users, ...agents, ...admins];

      const existingUserByEmail = allUsers.find(user => user.email === userData.email);
      if (existingUserByEmail) {
        console.log("User with email already exists:", userData.email);
        return res.status(400).json({ message: "User with this email already exists" });
      }

      const existingUserByUsername = allUsers.find(user => user.username === userData.username);
      if (existingUserByUsername) {
        console.log("User with username already exists:", userData.username);
        return res.status(400).json({ message: "User with this username already exists" });
      }

      // Generate unique ID for the user (agent or admin)
      const userIdPrefix = userRole === "admin" ? "admin" : "agent";
      const userId = `${userIdPrefix}_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;

      // Hash password before storing
      const hashedPassword = userData.password ? await passwordUtils.hash(userData.password) : null;

      // Create new user (agent or admin) using database storage
      const newUser = await storage.upsertUser({
        id: userId,
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        phoneNumber: userData.phoneNumber || null,
        username: userData.username || null,
        password: hashedPassword,
        profileImageUrl: userData.profileImageUrl || null,
        role: userRole,
        status: "active",
        verificationStatus: true,
        // Note: createdAt and updatedAt are omitted as they're auto-generated
      });

      // Initialize subscription for agents/admins (basic plan)
      try {
        await subscriptionMigrator.initializeUserSubscription(userId, 'basic');
        console.log(`✅ Subscription initialized for new ${userRole}: ${userId}`);
      } catch (subscriptionError) {
        console.warn(`⚠️ Failed to initialize subscription for ${userRole} ${userId}:`, subscriptionError);
        // Don't fail the creation if subscription initialization fails
      }

      // Log user creation activity
      await storage.createActivity({
        type: "registration",
        message: `New ${userRole} created: ${newUser.firstName} ${newUser.lastName} (${newUser.email})`,
        userId: newUser.id,
        metadata: JSON.stringify({ createdBy: "admin", userType: userRole }),
      });

      console.log(`New ${userRole} created in database:`, newUser);
      res.json(newUser);
    } catch (error) {
      console.error(`Error creating ${userRole}:`, error);
      res.status(500).json({ message: `Failed to create ${userRole}` });
    }
  });

  // Agent management endpoints
  app.post("/api/agents/:id/activate", async (req, res) => {
    try {
      const { id } = req.params;
      console.log(`Activating agent ${id}`);

      // Check if agent exists and has agent role
      const agent = await storage.getUser(id);
      if (!agent || agent.role !== "agent") {
        return res.status(404).json({ message: "Agent not found" });
      }

      // Update agent status in database
      const updatedAgent = await storage.updateUserStatus(id, "active");

      if (!updatedAgent) {
        return res.status(500).json({ message: "Failed to update agent status" });
      }

      // Log activity
      await storage.createActivity({
        type: "maintenance",
        message: `Agent ${updatedAgent.firstName} ${updatedAgent.lastName} activated`,
        userId: updatedAgent.id,
        metadata: JSON.stringify({ action: "activate", performedBy: "admin" }),
      });

      res.json({
        success: true,
        message: "Agent activated successfully",
        agent: updatedAgent
      });
    } catch (error) {
      console.error("Error activating agent:", error);
      res.status(500).json({ message: "Failed to activate agent" });
    }
  });

  app.post("/api/agents/:id/deactivate", async (req, res) => {
    try {
      const { id } = req.params;
      console.log(`Deactivating agent ${id}`);

      const agent = await storage.getUser(id);
      if (!agent || agent.role !== "agent") {
        return res.status(404).json({ message: "Agent not found" });
      }

      const updatedAgent = await storage.updateUserStatus(id, "inactive");

      if (!updatedAgent) {
        return res.status(500).json({ message: "Failed to update agent status" });
      }

      await storage.createActivity({
        type: "maintenance",
        message: `Agent ${updatedAgent.firstName} ${updatedAgent.lastName} deactivated`,
        userId: updatedAgent.id,
        metadata: JSON.stringify({ action: "deactivate", performedBy: "admin" }),
      });

      res.json({
        success: true,
        message: "Agent deactivated successfully",
        agent: updatedAgent
      });
    } catch (error) {
      console.error("Error deactivating agent:", error);
      res.status(500).json({ message: "Failed to deactivate agent" });
    }
  });

  app.post("/api/agents/:id/suspend", async (req, res) => {
    try {
      const { id } = req.params;
      console.log(`Suspending agent ${id}`);

      const agent = await storage.getUser(id);
      if (!agent || agent.role !== "agent") {
        return res.status(404).json({ message: "Agent not found" });
      }

      const updatedAgent = await storage.updateUserStatus(id, "suspended");

      if (!updatedAgent) {
        return res.status(500).json({ message: "Failed to update agent status" });
      }

      await storage.createActivity({
        type: "maintenance",
        message: `Agent ${updatedAgent.firstName} ${updatedAgent.lastName} suspended`,
        userId: updatedAgent.id,
        metadata: JSON.stringify({ action: "suspend", performedBy: "admin" }),
      });

      // Broadcast suspension event to force logout
      broadcast({
        type: "user_suspended",
        userId: id,
        reason: 'Account suspended by administrator'
      });

      res.json({
        success: true,
        message: "Agent suspended successfully",
        agent: updatedAgent
      });
    } catch (error) {
      console.error("Error suspending agent:", error);
      res.status(500).json({ message: "Failed to suspend agent" });
    }
  });

  app.post("/api/agents/:id/verify", async (req, res) => {
    try {
      const { id } = req.params;
      console.log(`Verifying agent ${id}`);

      const agent = await storage.getUser(id);
      if (!agent || agent.role !== "agent") {
        return res.status(404).json({ message: "Agent not found" });
      }

      const updatedAgent = await storage.updateUserVerification(id, true);

      if (!updatedAgent) {
        return res.status(500).json({ message: "Failed to update agent verification" });
      }

      await storage.createActivity({
        type: "maintenance",
        message: `Agent ${updatedAgent.firstName} ${updatedAgent.lastName} verified`,
        userId: updatedAgent.id,
        metadata: JSON.stringify({ action: "verify", performedBy: "admin" }),
      });

      res.json({
        success: true,
        message: "Agent verified successfully",
        agent: updatedAgent
      });
    } catch (error) {
      console.error("Error verifying agent:", error);
      res.status(500).json({ message: "Failed to verify agent" });
    }
  });

  app.post("/api/agents/:id/delete", async (req, res) => {
    try {
      const { id } = req.params;
      console.log(`Deleting agent ${id}`);

      const agent = await storage.getUser(id);
      if (!agent || agent.role !== "agent") {
        return res.status(404).json({ message: "Agent not found" });
      }

      // Store agent info for logging after deletion
      const agentInfo = {
        firstName: agent.firstName,
        lastName: agent.lastName,
        id: agent.id
      };

      const deleted = await storage.deleteUser(id);

      if (!deleted) {
        return res.status(500).json({ message: "Failed to delete agent" });
      }

      // Log activity after successful deletion (optional - don't fail if this fails)
      try {
        await storage.createActivity({
          type: "maintenance",
          message: `Agent ${agentInfo.firstName} ${agentInfo.lastName} deleted`,
          metadata: JSON.stringify({ action: "delete", performedBy: "admin", deletedAgentId: agentInfo.id }),
        });
      } catch (activityError) {
        console.warn("Failed to log agent deletion activity:", activityError);
        // Don't fail the request if activity logging fails
      }

      res.json({
        success: true,
        message: "Agent deleted successfully"
      });
    } catch (error) {
      console.error("Error deleting agent:", error);
      res.status(500).json({ message: "Failed to delete agent" });
    }
  });

  // General user management endpoints
  app.post("/api/users/:id/activate", async (req, res) => {
    try {
      const { id } = req.params;
      console.log(`Activating user ${id}`);

      const user = await storage.getUser(id);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      const updatedUser = await storage.updateUserStatus(id, "active");

      if (!updatedUser) {
        return res.status(500).json({ message: "Failed to update user status" });
      }

      await storage.createActivity({
        type: "maintenance",
        message: `User ${updatedUser.firstName} ${updatedUser.lastName} activated`,
        userId: updatedUser.id,
        metadata: JSON.stringify({ action: "activate", performedBy: "admin" }),
      });

      res.json({
        success: true,
        message: "User activated successfully",
        user: updatedUser
      });
    } catch (error) {
      console.error("Error activating user:", error);
      res.status(500).json({ message: "Failed to activate user" });
    }
  });

  app.post("/api/users/:id/deactivate", async (req, res) => {
    try {
      const { id } = req.params;
      console.log(`Deactivating user ${id}`);

      const user = await storage.getUser(id);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      const updatedUser = await storage.updateUserStatus(id, "inactive");

      if (!updatedUser) {
        return res.status(500).json({ message: "Failed to update user status" });
      }

      await storage.createActivity({
        type: "maintenance",
        message: `User ${updatedUser.firstName} ${updatedUser.lastName} deactivated`,
        userId: updatedUser.id,
        metadata: JSON.stringify({ action: "deactivate", performedBy: "admin" }),
      });

      res.json({
        success: true,
        message: "User deactivated successfully",
        user: updatedUser
      });
    } catch (error) {
      console.error("Error deactivating user:", error);
      res.status(500).json({ message: "Failed to deactivate user" });
    }
  });

  app.post("/api/users/:id/suspend", async (req, res) => {
    try {
      const { id } = req.params;
      console.log(`Suspending user ${id}`);

      const user = await storage.getUser(id);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      const updatedUser = await storage.updateUserStatus(id, "suspended");

      if (!updatedUser) {
        return res.status(500).json({ message: "Failed to update user status" });
      }

      await storage.createActivity({
        type: "maintenance",
        message: `User ${updatedUser.firstName} ${updatedUser.lastName} suspended`,
        userId: updatedUser.id,
        metadata: JSON.stringify({ action: "suspend", performedBy: "admin" }),
      });

      // Broadcast suspension event to force logout
      broadcast({
        type: "user_suspended",
        userId: id,
        reason: 'Account suspended by administrator'
      });

      res.json({
        success: true,
        message: "User suspended successfully",
        user: updatedUser
      });
    } catch (error) {
      console.error("Error suspending user:", error);
      res.status(500).json({ message: "Failed to suspend user" });
    }
  });

  app.post("/api/users/:id/verify", async (req, res) => {
    try {
      const { id } = req.params;
      console.log(`Verifying user ${id}`);

      const user = await storage.getUser(id);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      const updatedUser = await storage.updateUserVerification(id, true);

      if (!updatedUser) {
        return res.status(500).json({ message: "Failed to update user verification" });
      }

      await storage.createActivity({
        type: "maintenance",
        message: `User ${updatedUser.firstName} ${updatedUser.lastName} verified`,
        userId: updatedUser.id,
        metadata: JSON.stringify({ action: "verify", performedBy: "admin" }),
      });

      res.json({
        success: true,
        message: "User verified successfully",
        user: updatedUser
      });
    } catch (error) {
      console.error("Error verifying user:", error);
      res.status(500).json({ message: "Failed to verify user" });
    }
  });

  // Update user information
  app.put("/api/users/:id", async (req, res) => {
    try {
      const { id } = req.params;
      const userData = req.body;

      console.log(`Updating user ${id}:`, userData);

      const existingUser = await storage.getUser(id);
      if (!existingUser) {
        return res.status(404).json({ message: "User not found" });
      }

      const updatedUser = await storage.updateUser(id, userData);

      if (!updatedUser) {
        return res.status(500).json({ message: "Failed to update user" });
      }

      await storage.createActivity({
        type: "maintenance",
        message: `User ${updatedUser.firstName} ${updatedUser.lastName} information updated`,
        userId: updatedUser.id,
        metadata: JSON.stringify({ action: "update", performedBy: "admin", changes: userData }),
      });

      res.json(updatedUser);
    } catch (error) {
      console.error("Error updating user:", error);
      res.status(500).json({ message: "Failed to update user" });
    }
  });

  app.post("/api/users/:id/delete", async (req, res) => {
    try {
      const { id } = req.params;
      console.log(`Deleting user ${id}`);

      const user = await storage.getUser(id);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // Store user info for logging after deletion
      const userInfo = {
        firstName: user.firstName,
        lastName: user.lastName,
        id: user.id
      };

      const deleted = await storage.deleteUser(id);

      if (!deleted) {
        return res.status(500).json({ message: "Failed to delete user" });
      }

      // Log activity after successful deletion (optional - don't fail if this fails)
      try {
        await storage.createActivity({
          type: "maintenance",
          message: `User ${userInfo.firstName} ${userInfo.lastName} deleted`,
          metadata: JSON.stringify({ action: "delete", performedBy: "admin", deletedUserId: userInfo.id }),
        });
      } catch (activityError) {
        console.warn("Failed to log user deletion activity:", activityError);
        // Don't fail the request if activity logging fails
      }

      res.json({
        success: true,
        message: "User deleted successfully"
      });
    } catch (error) {
      console.error("Error deleting user:", error);
      res.status(500).json({ message: "Failed to delete user" });
    }
  });

  // User restriction endpoints
  app.post("/api/users/:id/restrict", async (req, res) => {
    try {
      const { id } = req.params;
      const { reason } = req.body;
      console.log(`Restricting user ${id} with reason: ${reason}`);

      const user = await storage.getUser(id);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // Update user status to restricted using storage method
      const updatedUser = await storage.updateUserStatus(id, "restricted");

      if (!updatedUser) {
        return res.status(500).json({ message: "Failed to update user status" });
      }

      // Update restriction details directly in database
      await db.update(schema.users)
        .set({
          restrictionReason: reason || 'Account restricted by administrator',
          restrictedAt: Date.now(),
          restrictedBy: 'admin', // TODO: Get actual admin ID from session
          updatedAt: Date.now()
        })
        .where(eq(schema.users.id, id));

      await storage.createActivity({
        type: "restriction",
        message: `User ${updatedUser.firstName} ${updatedUser.lastName} restricted: ${reason || 'No reason provided'}`,
        userId: updatedUser.id,
        metadata: JSON.stringify({ action: "restrict", reason, performedBy: "admin" }),
      });

      // Broadcast restriction event to force logout
      broadcast({
        type: "user_restricted",
        userId: id,
        reason: reason || 'Account restricted by administrator'
      });

      res.json({
        success: true,
        message: "User restricted successfully",
        user: updatedUser
      });
    } catch (error) {
      console.error("Error restricting user:", error);
      res.status(500).json({ message: "Failed to restrict user" });
    }
  });

  app.post("/api/users/:id/unrestrict", async (req, res) => {
    try {
      const { id } = req.params;
      console.log(`Unrestricting user ${id}`);

      const user = await storage.getUser(id);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // Update user status back to active
      const updatedUser = await storage.updateUserStatus(id, "active");

      if (!updatedUser) {
        return res.status(500).json({ message: "Failed to update user status" });
      }

      // Clear restriction details
      await db.update(schema.users)
        .set({
          restrictionReason: null,
          restrictedAt: null,
          restrictedBy: null,
          updatedAt: Date.now()
        })
        .where(eq(schema.users.id, id));

      await storage.createActivity({
        type: "unrestriction",
        message: `User ${updatedUser.firstName} ${updatedUser.lastName} unrestricted`,
        userId: updatedUser.id,
        metadata: JSON.stringify({ action: "unrestrict", performedBy: "admin" }),
      });

      res.json({
        success: true,
        message: "User unrestricted successfully",
        user: updatedUser
      });
    } catch (error) {
      console.error("Error unrestricting user:", error);
      res.status(500).json({ message: "Failed to unrestrict user" });
    }
  });

  // User status check endpoint for real-time restriction monitoring
  app.get("/api/users/:id/status", async (req, res) => {
    try {
      const { id } = req.params;
      const user = await storage.getUser(id);

      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      res.json({
        id: user.id,
        username: user.username,
        status: user.status,
        restrictionReason: user.restrictionReason || null,
        restrictedAt: user.restrictedAt || null,
        restrictedBy: user.restrictedBy || null
      });
    } catch (error) {
      console.error("Error checking user status:", error);
      res.status(500).json({ message: "Failed to check user status" });
    }
  });

  // Admin notification endpoints
  app.get("/api/admin/notifications", async (_req, res) => {
    try {
      // Get recent activities and format as notifications
      const activities = await storage.getActivities();

      const notifications = activities
        .filter(activity => {
          // Filter for notification-worthy activities
          return ['booking', 'task_accepted', 'task_rejected', 'task_completed', 'authentication'].includes(activity.type);
        })
        .slice(0, 50) // Limit to 50 most recent
        .map(activity => {
          let notificationType = 'system';
          let title = 'System Activity';
          let priority = 'medium';

          switch (activity.type) {
            case 'booking':
              notificationType = 'locker_rental';
              title = 'New Locker Rental';
              priority = 'high';
              break;
            case 'task_accepted':
              notificationType = 'task_accepted';
              title = 'Task Accepted';
              priority = 'medium';
              break;
            case 'task_rejected':
              notificationType = 'task_rejected';
              title = 'Task Rejected';
              priority = 'high';
              break;
            case 'task_completed':
              notificationType = 'task_completed';
              title = 'Task Completed';
              priority = 'medium';
              break;
            case 'authentication':
              notificationType = 'user_registered';
              title = 'User Activity';
              priority = 'low';
              break;
          }

          return {
            id: `notif_${activity.id}`,
            type: notificationType,
            title,
            message: activity.message,
            userId: activity.userId,
            userName: activity.userId ? `User ${activity.userId.slice(-6)}` : undefined,
            lockerId: activity.lockerId?.toString(),
            lockerCode: activity.lockerId ? `L${activity.lockerId.toString().padStart(3, '0')}` : undefined,
            taskId: activity.metadata ? JSON.parse(activity.metadata).taskId : undefined,
            timestamp: activity.createdAt,
            read: false,
            priority
          };
        });

      res.json(notifications);
    } catch (error) {
      console.error("Error fetching admin notifications:", error);
      res.status(500).json({ error: "Failed to fetch notifications" });
    }
  });

  app.post("/api/admin/notifications/:id/read", async (_req, res) => {
    try {
      // In a real implementation, you'd store read status in database
      // For now, just return success
      res.json({ success: true });
    } catch (error) {
      console.error("Error marking notification as read:", error);
      res.status(500).json({ error: "Failed to mark notification as read" });
    }
  });

  app.post("/api/admin/notifications/mark-all-read", async (_req, res) => {
    try {
      // In a real implementation, you'd mark all notifications as read in database
      res.json({ success: true });
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
      res.status(500).json({ error: "Failed to mark all notifications as read" });
    }
  });

  app.delete("/api/admin/notifications/:id", async (_req, res) => {
    try {
      // In a real implementation, you'd delete the notification from database
      res.json({ success: true });
    } catch (error) {
      console.error("Error deleting notification:", error);
      res.status(500).json({ error: "Failed to delete notification" });
    }
  });

  // Chat endpoints
  app.get("/api/chat/:fromUserId/:toUserId", async (req, res) => {
    try {
      const { fromUserId, toUserId } = req.params;
      const { bookingId } = req.query;
      
      const messages = await storage.getChatMessages(
        fromUserId,
        toUserId,
        bookingId ? parseInt(bookingId as string) : undefined
      );
      res.json(messages);
    } catch (error) {
      console.error("Error fetching chat messages:", error);
      res.status(500).json({ message: "Failed to fetch chat messages" });
    }
  });

  app.post("/api/chat", async (req, res) => {
    try {
      const messageData = insertChatMessageSchema.parse(req.body);
      const message = await storage.createChatMessage(messageData);

      broadcast({ type: "chat_message", message });
      res.json(message);
    } catch (error) {
      console.error("Error sending chat message:", error);
      res.status(500).json({ message: "Failed to send chat message" });
    }
  });

  // Demo payment endpoints
  app.post("/api/demo-payment", async (req, res) => {
    try {
      const { amount, baseAmount, agentCommission, agentId, description, userId, lockerId, startTime, endTime, duration } = req.body;

      console.log("Processing demo payment:", { amount, baseAmount, agentCommission, agentId, description, userId, lockerId, startTime, endTime, duration });

      // Validate that user exists
      if (userId) {
        const user = await storage.getUser(userId);
        if (!user) {
          return res.status(400).json({ message: "User not found" });
        }
      }

      // Validate that locker exists
      if (lockerId) {
        const locker = await storage.getLocker(parseInt(lockerId));
        if (!locker) {
          return res.status(400).json({ message: "Locker not found" });
        }
      }

      // Create booking immediately (remove setTimeout for debugging)
      try {
        // Create a demo booking with simulated payment using provided date/time
        const booking = await storage.createBooking({
          userId: userId || "demo_user",
          lockerId: parseInt(lockerId) || 1,
          startTime: startTime || Date.now(),
          endTime: endTime || (Date.now() + (2 * 60 * 60 * 1000)),
          duration: duration ? (duration * 60) : 120, // Convert hours to minutes
          totalCost: parseFloat(amount.toString()),
          paymentStatus: "simulated_paid",
          paymentMethod: "demo_card",
          status: "active",
          agentId: agentId || null,
          agentCommission: agentCommission || 0,
        });

        console.log("Booking created:", booking.id);

        // Update locker status to occupied
        if (lockerId) {
          await storage.updateLockerStatus(parseInt(lockerId), "occupied");
        }

        // Create access session for the demo booking
        const now = Date.now();
        const sessionEnd = booking.endTime || (now + (booking.duration || 120) * 60 * 1000); // duration is in minutes, convert to milliseconds
        const graceStart = sessionEnd;
        const graceEnd = graceStart + (20 * 60 * 1000); // Default 20 minutes grace period

        const accessSessionData = {
          bookingId: booking.id,
          userId: booking.userId,
          agentId: null,
          lockerId: booking.lockerId,
          accessMethod: 'demo_booking',
          sessionStart: booking.startTime,
          sessionEnd,
          graceStart,
          graceEnd,
          status: 'active' as const,
          warningsSent: 0,
          metadata: JSON.stringify({
            grantedBy: 'demo_payment_system',
            bookingId: booking.id,
            timestamp: new Date().toISOString()
          })
        };

        try {
          const accessSession = await storage.createLockerAccessSession(accessSessionData);
          console.log(`✅ Created access session ${accessSession.id} for demo booking ${booking.id}`);
        } catch (sessionError) {
          console.error(`❌ Failed to create access session for demo booking ${booking.id}:`, sessionError);
          // Don't fail the booking creation if access session fails
        }

        // Automatically unlock the locker for the user
        try {
          const unlockSuccess = sendCommandToESP32(booking.lockerId, "unlock", {
            userId: booking.userId,
            duration: booking.duration || 120,
            bookingId: booking.id
          });

          if (unlockSuccess) {
            console.log(`✅ Unlock command sent to locker ${booking.lockerId} for demo booking ${booking.id}`);

            // Log unlock activity
            await storage.createActivity({
              type: "access",
              message: `Locker ${booking.lockerId} automatically unlocked for demo booking`,
              lockerId: booking.lockerId,
              userId: booking.userId,
              metadata: JSON.stringify({
                bookingId: booking.id,
                autoUnlock: true,
                demoPayment: true,
                timestamp: new Date().toISOString()
              }),
            });
          } else {
            console.warn(`⚠️ Failed to send unlock command to locker ${booking.lockerId} - device may be offline`);
          }
        } catch (unlockError) {
          console.error(`❌ Error unlocking locker ${booking.lockerId}:`, unlockError);
          // Don't fail the booking creation if unlock fails
        }

        // Get locker and user information for task creation
        const locker = await storage.getLocker(parseInt(lockerId));
        const user = await storage.getUser(userId || "demo_user");
        const location = locker?.locationId ? await storage.getLocation(locker.locationId) : null;

        // Create a task for agents to handle the booking
        console.log("Attempting to create task for booking:", booking.id);
        const availableAgents = await storage.getUsersByRole("agent");
        console.log("Available agents:", availableAgents.length);

        // If a specific agent was selected for commission, assign to them; otherwise use first available
        let assignedAgent = null;
        if (agentId) {
          assignedAgent = availableAgents.find(agent => agent.id === agentId);
          console.log("Agent selected for commission:", assignedAgent?.username);
        }

        if (!assignedAgent && availableAgents.length > 0) {
          // For now, assign to the first available agent (could be improved with load balancing)
          assignedAgent = availableAgents[0];
          console.log("Assigning task to agent:", assignedAgent.username);
        }

        if (assignedAgent) {

          try {
            const taskEarnings = agentCommission > 0 ? agentCommission : 15.0; // Use commission or default $15
            const task = await storage.createTask({
              agentId: assignedAgent.id,
              bookingId: booking.id,
              type: "delivery",
              description: `New locker booking assistance required for ${user?.firstName || 'User'} at ${location?.name || 'Main Building'}${agentCommission > 0 ? ' (Commission-based)' : ''}`,
              status: "new",
              priority: "medium",
              estimatedTime: 30, // 30 minutes estimated
              earnings: taskEarnings,
            });

            console.log("Task created successfully:", task.id);

            // Broadcast new task to agents
            broadcast({
              type: "new_task",
              data: {
                ...task,
                booking,
                locker,
                user,
                location
              }
            });

            console.log(`Created task ${task.id} for agent ${assignedAgent.username} for booking ${booking.id}`);
          } catch (taskError) {
            console.error("Error creating task:", taskError);
          }
        } else {
          console.log("No agents available to assign task");
        }

        // Log payment activity
        await storage.createActivity({
          type: "booking",
          message: `Demo payment processed: $${amount} for ${description}${agentCommission > 0 ? ` (Agent Commission: $${agentCommission})` : ''}`,
          userId: userId || "demo_user",
          lockerId: parseInt(lockerId) || 1,
          metadata: JSON.stringify({
            amount,
            baseAmount,
            agentCommission,
            agentId,
            description,
            timestamp: new Date().toISOString()
          }),
        });

        broadcast({ type: "payment_processed", booking });

        // Return the booking ID in the response
        res.json({
          status: "processing",
          message: "Demo payment is being processed",
          bookingId: booking.id
        });
      } catch (error) {
        console.error("Error creating booking after payment:", error);
        res.json({
          status: "processing",
          message: "Demo payment is being processed"
        });
      }
    } catch (error) {
      console.error("Error processing demo payment:", error);
      res.status(500).json({ message: "Failed to process demo payment" });
    }
  });

  // ESP32 endpoints for hardware communication
  app.post("/api/esp32/command", async (req, res) => {
    try {
      const { command, lockerId } = req.body;
      const lockerIdNum = parseInt(lockerId);
      
      // Log the ESP32 command
      await storage.createActivity({
        type: "access",
        message: `ESP32 command sent: ${command} to locker ${lockerId}`,
        lockerId: lockerIdNum,
        metadata: JSON.stringify({ command, timestamp: new Date().toISOString() }),
      });

      // Try to send command to real ESP32
      const sent = sendCommandToESP32(lockerIdNum, command);
      
      const response = {
        status: sent ? "sent" : "device_offline",
        lockerId,
        command,
        timestamp: new Date().toISOString(),
      };

      broadcast({ type: "esp32_command", response });
      res.json(response);
    } catch (error) {
      console.error("Error processing ESP32 command:", error);
      res.status(500).json({ message: "Failed to process ESP32 command" });
    }
  });

  // ESP32 ping endpoint for connection testing
  app.post("/api/esp32/ping", async (req, res) => {
    try {
      const { lockerId } = req.body;
      const lockerIdNum = parseInt(lockerId);

      // Log ping command
      await storage.createActivity({
        type: "access",
        message: `ESP32 ping sent to locker ${lockerId}`,
        lockerId: lockerIdNum,
        metadata: JSON.stringify({ command: "ping", timestamp: new Date().toISOString() }),
      });

      // Try to ping real ESP32
      const sent = sendCommandToESP32(lockerIdNum, "ping");

      // If command was sent successfully, update the ping time
      if (sent) {
        await storage.updateLockerPing(lockerIdNum);
      }

      // For now, we'll simulate a response since we don't wait for the actual response
      const responseTime = sent ? Math.floor(Math.random() * 100) + 10 : 0;

      const response = {
        status: sent ? "success" : "device_offline",
        lockerId,
        command: "ping",
        responseTime,
        timestamp: new Date().toISOString(),
      };

      broadcast({ type: "esp32_ping", response });
      res.json(response);
    } catch (error) {
      console.error("Error pinging ESP32:", error);
      res.status(500).json({ message: "Failed to ping ESP32" });
    }
  });

  // ESP32 reset endpoint
  app.post("/api/esp32/reset", async (req, res) => {
    try {
      const { lockerId } = req.body;
      const lockerIdNum = parseInt(lockerId);
      
      // Log reset command
      await storage.createActivity({
        type: "maintenance",
        message: `ESP32 reset command sent to locker ${lockerId}`,
        lockerId: lockerIdNum,
        metadata: JSON.stringify({ command: "reset", timestamp: new Date().toISOString() }),
      });

      // Try to send reset command to real ESP32
      const sent = sendCommandToESP32(lockerIdNum, "reset");
      
      const response = {
        status: sent ? "success" : "device_offline",
        lockerId,
        command: "reset",
        timestamp: new Date().toISOString(),
      };

      broadcast({ type: "esp32_reset", response });
      res.json(response);
    } catch (error) {
      console.error("Error resetting ESP32:", error);
      res.status(500).json({ message: "Failed to reset ESP32" });
    }
  });

  // ESP32 commands history endpoint
  app.get("/api/esp32/commands", async (_req, res) => {
    try {
      // Get recent ESP32 related activities
      const activities = await storage.getActivities(50);
      const esp32Commands = activities.filter(activity =>
        activity.type === "access" || activity.type === "maintenance"
      );

      res.json(esp32Commands);
    } catch (error) {
      console.error("Error fetching ESP32 commands:", error);
      res.status(500).json({ message: "Failed to fetch ESP32 commands" });
    }
  });

  // ESP32 biometric unlock endpoint
  app.post("/api/esp32/unlock", async (req, res) => {
    try {
      const { lockerId, authMethod } = req.body;
      const lockerIdNum = parseInt(lockerId);
      
      // Log biometric unlock attempt
      await storage.createActivity({
        type: "access",
        message: `Biometric unlock attempt using ${authMethod} for locker ${lockerId}`,
        lockerId: lockerIdNum,
        metadata: JSON.stringify({
          authMethod,
          timestamp: new Date().toISOString(),
          biometric: true
        }),
      });

      // Try to send unlock command to real ESP32
      const sent = sendCommandToESP32(lockerIdNum, "unlock", { authMethod });
      
      // Update locker status to unlocked if command was sent
      if (sent) {
        await storage.updateLockerStatus(lockerIdNum, "unlocked");
      }

      const response = {
        status: sent ? "success" : "device_offline",
        lockerId,
        command: "unlock",
        authMethod,
        timestamp: new Date().toISOString(),
        unlocked: sent,
      };

      broadcast({ type: "esp32_unlock", response });
      res.json(response);
    } catch (error) {
      console.error("Error processing biometric unlock:", error);
      res.status(500).json({ message: "Failed to process biometric unlock" });
    }
  });

  // Add new endpoint to get connected ESP32 devices
  app.get("/api/esp32/connected", async (_req, res) => {
    try {
      const connectedDevices = Array.from(esp32Clients.keys());
      res.json({
        count: connectedDevices.length,
        devices: connectedDevices
      });
    } catch (error) {
      console.error("Error fetching connected ESP32 devices:", error);
      res.status(500).json({ message: "Failed to fetch connected ESP32 devices" });
    }
  });

  // Agent task endpoints
  app.post("/api/tasks/:id/accept", async (req, res) => {
    try {
      const { id } = req.params;
      const task = await storage.updateTaskStatus(parseInt(id), "in_progress");
      
      if (task) {
        await storage.createActivity({
          type: "task_accepted",
          message: `Task #${task.id} accepted by agent`,
          metadata: JSON.stringify({ taskId: task.id, agentAction: "accept" }),
        });

        // Send admin notification
        const notificationData = {
          type: 'admin_notification',
          notificationType: 'task_accepted',
          title: 'Task Accepted',
          message: `Agent has accepted task #${task.id}`,
          taskId: task.id.toString(),
          userId: task.agentId,
          userName: `Agent ${task.agentId?.slice(-6) || 'Unknown'}`,
          priority: 'medium',
          timestamp: Date.now()
        };

        broadcast({ type: "task_update", task });
        broadcast(notificationData);
        res.json(task);
      } else {
        res.status(404).json({ message: "Task not found" });
      }
    } catch (error) {
      console.error("Error accepting task:", error);
      res.status(500).json({ message: "Failed to accept task" });
    }
  });

  app.post("/api/tasks/:id/complete", async (req, res) => {
    try {
      const { id } = req.params;
      const task = await storage.updateTaskStatus(parseInt(id), "completed");

      if (task) {
        await storage.createActivity({
          type: "task_completed",
          message: `Task #${task.id} completed by agent`,
          metadata: JSON.stringify({ taskId: task.id, agentAction: "complete", earnings: task.earnings }),
        });

        // Send admin notification
        const notificationData = {
          type: 'admin_notification',
          notificationType: 'task_completed',
          title: 'Task Completed',
          message: `Agent has completed task #${task.id} - Earned $${task.earnings || 0}`,
          taskId: task.id.toString(),
          userId: task.agentId,
          userName: `Agent ${task.agentId?.slice(-6) || 'Unknown'}`,
          priority: 'medium',
          timestamp: Date.now()
        };

        broadcast({ type: "task_update", task });
        broadcast(notificationData);
        res.json(task);
      } else {
        res.status(404).json({ message: "Task not found" });
      }
    } catch (error) {
      console.error("Error completing task:", error);
      res.status(500).json({ message: "Failed to complete task" });
    }
  });

  app.put("/api/tasks/:id", async (req, res) => {
    try {
      const { id } = req.params;
      const { status } = req.body;
      const task = await storage.updateTaskStatus(parseInt(id), status);

      if (task) {
        await storage.createActivity({
          type: "task",
          message: `Task #${task.id} status updated to ${status}`,
          metadata: JSON.stringify({ taskId: task.id, newStatus: status }),
        });

        broadcast({ type: "task_update", task });
        res.json(task);
      } else {
        res.status(404).json({ message: "Task not found" });
      }
    } catch (error) {
      console.error("Error updating task:", error);
      res.status(500).json({ message: "Failed to update task" });
    }
  });

  // Chat message endpoints
  app.get("/api/chat/messages", async (req, res) => {
    try {
      const { fromUserId, toUserId, bookingId } = req.query;

      if (!fromUserId || !toUserId) {
        return res.status(400).json({ message: "fromUserId and toUserId are required" });
      }

      const messages = await storage.getChatMessages(
        fromUserId as string,
        toUserId as string,
        bookingId ? parseInt(bookingId as string) : undefined
      );

      res.json(messages);
    } catch (error) {
      console.error("Error fetching chat messages:", error);
      res.status(500).json({ message: "Failed to fetch chat messages" });
    }
  });

  app.post("/api/chat/messages", async (req, res) => {
    try {
      console.log("Received chat message request:", req.body);
      const messageData = insertChatMessageSchema.parse(req.body);
      console.log("Parsed message data:", messageData);

      const message = await storage.createChatMessage(messageData);
      console.log("Created message:", message);

      // Broadcast the new message to connected clients
      broadcast({
        type: "new_message",
        message,
        toUserId: messageData.toUserId,
        fromUserId: messageData.fromUserId,
        bookingId: messageData.bookingId
      });

      res.json(message);
    } catch (error) {
      console.error("Error creating chat message:", error);
      console.error("Request body:", req.body);
      res.status(500).json({ message: "Failed to create chat message" });
    }
  });

  // Get chat messages between two users (support chat)
  app.get("/api/chat/messages", async (req, res) => {
    try {
      const { fromUserId, toUserId } = req.query;

      if (!fromUserId || !toUserId) {
        return res.status(400).json({ message: "fromUserId and toUserId are required" });
      }

      const messages = await storage.getChatMessages(
        fromUserId as string,
        toUserId as string
      );

      res.json(messages);
    } catch (error) {
      console.error("Error fetching chat messages:", error);
      res.status(500).json({ message: "Failed to fetch chat messages" });
    }
  });

  // Get support conversations for admin
  app.get("/api/chat/support/conversations/:adminId", async (req, res) => {
    try {
      const { adminId } = req.params;
      console.log("Fetching support conversations for admin:", adminId);
      const conversations = await storage.getSupportConversations(adminId);
      console.log("Found conversations:", conversations.length);
      res.json(conversations);
    } catch (error) {
      console.error("Error fetching support conversations:", error);
      res.status(500).json({ message: "Failed to fetch support conversations" });
    }
  });

  // Update support conversation status
  app.patch("/api/chat/support/conversations/:userId/status", async (req, res) => {
    try {
      const { userId } = req.params;
      const { status } = req.body;

      console.log(`Updating conversation status for user ${userId} to ${status}`);

      // Validate status
      if (!['resolved', 'suspended', 'active'].includes(status)) {
        return res.status(400).json({ message: "Invalid status. Must be 'resolved', 'suspended', or 'active'" });
      }

      // Get the user to verify they exist
      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // For now, we'll store conversation status in user metadata or create a separate table
      // Since we don't have a conversations table, we'll use the user's status field
      // or create a simple in-memory store for conversation statuses

      // Update user status if suspending/resolving affects the user
      if (status === 'suspended') {
        await storage.updateUserStatus(userId, "suspended");

        // Broadcast suspension event to force logout
        broadcast({
          type: "user_suspended",
          userId: userId,
          reason: 'Account suspended due to support conversation'
        });
      } else if (status === 'active') {
        await storage.updateUserStatus(userId, "active");
      }

      // Log the activity
      await storage.createActivity({
        type: "maintenance",
        message: `Support conversation with ${user.firstName} ${user.lastName} marked as ${status}`,
        userId: user.id,
        metadata: JSON.stringify({
          action: "conversation_status_update",
          newStatus: status,
          performedBy: "admin"
        }),
      });

      res.json({
        success: true,
        message: `Conversation status updated to ${status}`,
        userId: userId,
        status: status
      });

    } catch (error) {
      console.error("Error updating conversation status:", error);
      res.status(500).json({ message: "Failed to update conversation status" });
    }
  });

  // Mark messages as read
  app.post("/api/chat/messages/mark-read", async (req, res) => {
    try {
      const { fromUserId, toUserId } = req.body;

      if (!fromUserId || !toUserId) {
        return res.status(400).json({ message: "fromUserId and toUserId are required" });
      }

      await storage.markMessagesAsRead(fromUserId, toUserId);
      res.json({ success: true });
    } catch (error) {
      console.error("Error marking messages as read:", error);
      res.status(500).json({ message: "Failed to mark messages as read" });
    }
  });

  // Agent statistics endpoint
  app.get("/api/agent/stats", async (req, res) => {
    try {
      const { agentId } = req.query;

      // Get all tasks for the agent or all tasks if no agentId specified
      const tasks = agentId
        ? await storage.getTasksByAgent(agentId as string)
        : await storage.getTasks();

      // Get all bookings to calculate additional metrics
      const bookings = await storage.getBookings();
      const agentBookings = agentId
        ? bookings.filter(booking => {
            // Find tasks for this agent that are linked to bookings
            const agentTasks = tasks.filter(task => task.agentId === agentId);
            return agentTasks.some(task => task.bookingId === booking.id);
          })
        : bookings;

      // Calculate statistics
      const totalTasks = tasks.length;
      const completedTasks = tasks.filter(task => task.status === 'completed').length;
      const activeTasks = tasks.filter(task => task.status === 'in_progress').length;
      const availableTasks = tasks.filter(task => task.status === 'new').length;

      // Calculate earnings from completed tasks
      const totalEarnings = tasks
        .filter(task => task.status === 'completed')
        .reduce((sum, task) => sum + (parseFloat(task.earnings?.toString() || '0')), 0);

      // Calculate today's earnings
      const today = new Date();
      const todayEarnings = tasks
        .filter(task => {
          const taskDate = new Date(task.updatedAt || task.createdAt || Date.now());
          return task.status === 'completed' &&
                 taskDate.toDateString() === today.toDateString();
        })
        .reduce((sum, task) => sum + (parseFloat(task.earnings?.toString() || '0')), 0);

      // Calculate rating based on completed tasks (simple algorithm)
      // Rating = 5.0 - (failed_tasks / total_tasks) * 2
      // This gives a rating between 3.0 and 5.0 based on success rate
      const failedTasks = totalTasks - completedTasks - activeTasks; // tasks that were cancelled/failed
      const averageRating = totalTasks > 0
        ? Math.max(3.0, 5.0 - (failedTasks / totalTasks) * 2)
        : 5.0;

      const stats = {
        totalTasks,
        completedTasks,
        activeTasks,
        availableTasks,
        totalEarnings: parseFloat(totalEarnings.toFixed(2)),
        todayEarnings: parseFloat(todayEarnings.toFixed(2)),
        successRate: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0,
        averageRating: parseFloat(averageRating.toFixed(1)),
        totalBookings: agentBookings.length,
        completedBookings: agentBookings.filter(booking => booking.status === 'completed').length,
      };

      res.json(stats);
    } catch (error) {
      console.error("Error fetching agent stats:", error);
      res.status(500).json({ message: "Failed to fetch agent statistics" });
    }
  });



  app.get("/api/esp32/status/:lockerId", async (req, res) => {
    try {
      const { lockerId } = req.params;
      const locker = await storage.getLockerByCode(lockerId);

      if (!locker) {
        return res.status(404).json({ message: "Locker not found" });
      }

      // Return real ESP32 status data
      const status = {
        lockerId: locker.code,
        status: locker.status,
        battery: null, // Will be updated when ESP32 sends real data
        signal: null, // Will be updated when ESP32 sends real data
        lastPing: locker.lastPing ? new Date(locker.lastPing).toISOString() : null,
      };

      res.json(status);
    } catch (error) {
      console.error("Error fetching ESP32 status:", error);
      res.status(500).json({ message: "Failed to fetch ESP32 status" });
    }
  });

  // Swift Agent API Routes

  // Check if Swift Agent system is ready
  app.get("/api/swift-agent/status", async (_req, res) => {
    try {
      // Check if the required tables exist
      const tablesExist = await storage.checkSwiftAgentTables();
      res.json({
        ready: tablesExist,
        message: tablesExist ? 'Swift Agent system is ready' : 'Swift Agent system is initializing'
      });
    } catch (error) {
      console.error("Error checking Swift Agent status:", error);
      res.json({ ready: false, message: 'Swift Agent system is not available' });
    }
  });

  // Register agent biometric
  app.post("/api/swift-agent/biometric/register", async (req, res) => {
    try {
      const { agentId, biometricType, biometricData } = req.body;

      if (!agentId || !biometricType || !biometricData) {
        return res.status(400).json({
          message: "agentId, biometricType, and biometricData are required"
        });
      }

      const result = await swiftAgentService.registerAgentBiometric(
        agentId,
        biometricType,
        biometricData
      );

      if (result.success) {
        res.json({ success: true, message: result.message });
      } else {
        res.status(400).json({ success: false, message: result.message });
      }
    } catch (error) {
      console.error("Error registering agent biometric:", error);
      res.status(500).json({ message: "Failed to register agent biometric" });
    }
  });

  // Authenticate agent biometric
  app.post("/api/swift-agent/biometric/authenticate", async (req, res) => {
    try {
      const { biometricType, biometricData } = req.body;

      if (!biometricType || !biometricData) {
        return res.status(400).json({
          message: "biometricType and biometricData are required"
        });
      }

      const result = await swiftAgentService.authenticateAgentBiometric(
        biometricType,
        biometricData
      );

      res.json(result);
    } catch (error) {
      console.error("Error authenticating agent biometric:", error);
      res.status(500).json({ message: "Failed to authenticate agent biometric" });
    }
  });

  // Grant agent access to locker
  app.post("/api/swift-agent/access/grant", async (req, res) => {
    try {
      const { bookingId, agentId, lockerId, accessMethod } = req.body;

      if (!bookingId || !agentId || !lockerId) {
        return res.status(400).json({
          message: "bookingId, agentId, and lockerId are required"
        });
      }

      const result = await swiftAgentService.grantAgentAccess(
        parseInt(bookingId),
        agentId,
        parseInt(lockerId),
        accessMethod
      );

      if (result.success) {
        res.json(result);
      } else {
        res.status(400).json(result);
      }
    } catch (error) {
      console.error("Error granting agent access:", error);
      res.status(500).json({ message: "Failed to grant agent access" });
    }
  });

  // Get active access sessions
  app.get("/api/swift-agent/sessions/active", async (_req, res) => {
    try {
      const sessions = swiftAgentService.getActiveSessions();
      res.json(sessions);
    } catch (error) {
      console.error("Error fetching active sessions:", error);
      res.status(500).json({ message: "Failed to fetch active sessions" });
    }
  });

  // Revoke access session (admin only)
  app.post("/api/swift-agent/sessions/:sessionId/revoke", async (req, res) => {
    try {
      const { sessionId } = req.params;
      const { revokedBy } = req.body;

      if (!revokedBy) {
        return res.status(400).json({ message: "revokedBy is required" });
      }

      const success = await swiftAgentService.revokeSession(
        parseInt(sessionId),
        revokedBy
      );

      if (success) {
        res.json({ success: true, message: "Session revoked successfully" });
      } else {
        res.status(404).json({ success: false, message: "Session not found" });
      }
    } catch (error) {
      console.error("Error revoking session:", error);
      res.status(500).json({ message: "Failed to revoke session" });
    }
  });

  // Get access sessions by user
  app.get("/api/swift-agent/sessions/user/:userId", async (req, res) => {
    try {
      const { userId } = req.params;
      const sessions = await storage.getAccessSessionsByUser(userId);
      res.json(sessions);
    } catch (error) {
      console.error("Error fetching user sessions:", error);
      res.status(500).json({ message: "Failed to fetch user sessions" });
    }
  });

  // Get access sessions by agent
  app.get("/api/swift-agent/sessions/agent/:agentId", async (req, res) => {
    try {
      const { agentId } = req.params;
      const sessions = await storage.getAccessSessionsByAgent(agentId);
      res.json(sessions);
    } catch (error) {
      console.error("Error fetching agent sessions:", error);
      res.status(500).json({ message: "Failed to fetch agent sessions" });
    }
  });

  // Get expiration notifications for a session
  app.get("/api/swift-agent/notifications/:sessionId", async (req, res) => {
    try {
      const { sessionId } = req.params;
      const notifications = await storage.getExpirationNotifications(parseInt(sessionId));
      res.json(notifications);
    } catch (error) {
      console.error("Error fetching notifications:", error);
      res.status(500).json({ message: "Failed to fetch notifications" });
    }
  });

  // Acknowledge notification
  app.post("/api/swift-agent/notifications/:notificationId/acknowledge", async (req, res) => {
    try {
      const { notificationId } = req.params;
      const notification = await storage.markNotificationAcknowledged(parseInt(notificationId));

      if (notification) {
        res.json({ success: true, notification });
      } else {
        res.status(404).json({ success: false, message: "Notification not found" });
      }
    } catch (error) {
      console.error("Error acknowledging notification:", error);
      res.status(500).json({ message: "Failed to acknowledge notification" });
    }
  });

  // Security Dashboard (Admin only)
  app.get("/api/security/dashboard",
    ...requireAdmin,
    async (_req, res) => {
    try {
      const dashboard = getSecurityDashboard();
      res.json(dashboard);
    } catch (error) {
      console.error("Error fetching security dashboard:", error);
      res.status(500).json({ message: "Failed to fetch security dashboard" });
    }
  });

  // Get detailed locker information for admin
  app.get("/api/admin/lockers/detailed", async (_req, res) => {
    try {
      const lockers = await storage.getLockers();
      const detailedLockers = [];

      for (const locker of lockers) {
        const lockerData: any = { ...locker };

        // Get active booking for this locker
        const bookings = await storage.getBookingsByLocker(locker.id);
        const activeBooking = bookings.find(b => b.status === 'active' || b.status === 'confirmed');

        let accessSession = null;

        if (activeBooking) {
          lockerData.booking = activeBooking;

          // Get user information
          const user = await storage.getUser(activeBooking.userId);
          if (user) {
            lockerData.booking.user = user;
          }

          // Get agent information if there's a task
          const tasks = await storage.getTasksByBooking(activeBooking.id);
          const activeTask = tasks.find(t => t.status === 'accepted' || t.status === 'in_progress');

          if (activeTask) {
            lockerData.booking.task = activeTask;
            const agent = await storage.getUser(activeTask.agentId);
            if (agent) {
              lockerData.booking.agent = agent;
            }
          }

          // Get access session information
          const sessions = await storage.getActiveAccessSessions();
          accessSession = sessions.find(s => s.lockerId === locker.id && s.bookingId === activeBooking.id);
          if (accessSession) {
            lockerData.accessSession = accessSession;
          }
        }

        // Calculate dynamic status based on current conditions
        const dynamicStatus = await storage.calculateLockerStatus(locker, activeBooking, accessSession);
        lockerData.status = dynamicStatus;

        detailedLockers.push(lockerData);
      }

      res.json(detailedLockers);
    } catch (error) {
      console.error("Error fetching detailed locker information:", error);
      res.status(500).json({ message: "Failed to fetch detailed locker information" });
    }
  });

  // Create missing access sessions for existing bookings (migration endpoint)
  app.post("/api/admin/create-missing-access-sessions", async (req, res) => {
    try {
      const allBookings = await storage.getBookings();
      let created = 0;
      let skipped = 0;

      for (const booking of allBookings) {
        // Check if access session already exists
        const userSessions = await storage.getAccessSessionsByUser(booking.userId);
        const existingSession = userSessions.find(session => session.bookingId === booking.id);

        if (existingSession) {
          skipped++;
          continue;
        }

        // Create access session for this booking
        const now = Date.now();
        const sessionEnd = booking.endTime || (now + (booking.duration || 120) * 60 * 1000);
        const graceStart = sessionEnd;
        const graceEnd = graceStart + (20 * 60 * 1000); // Default 20 minutes grace period

        const accessSessionData = {
          bookingId: booking.id,
          userId: booking.userId,
          agentId: null,
          lockerId: booking.lockerId,
          accessMethod: 'migration_created',
          sessionStart: booking.startTime,
          sessionEnd,
          graceStart,
          graceEnd,
          status: booking.status === 'active' ? 'active' : 'expired',
          warningsSent: 0,
          metadata: JSON.stringify({
            grantedBy: 'migration_endpoint',
            bookingId: booking.id,
            migrationDate: new Date().toISOString()
          })
        };

        await storage.createLockerAccessSession(accessSessionData);
        created++;
      }

      res.json({
        success: true,
        message: `Migration completed: ${created} access sessions created, ${skipped} skipped`,
        created,
        skipped,
        total: allBookings.length
      });
    } catch (error) {
      console.error("Error creating missing access sessions:", error);
      res.status(500).json({ message: "Failed to create missing access sessions" });
    }
  });

  // Adjust grace period for a session
  app.post("/api/admin/sessions/:sessionId/adjust-grace-period", async (req, res) => {
    try {
      const { sessionId } = req.params;
      const { gracePeriodMinutes } = req.body;

      if (!gracePeriodMinutes || gracePeriodMinutes < 0 || gracePeriodMinutes > 120) {
        return res.status(400).json({ message: "Invalid grace period. Must be between 0 and 120 minutes." });
      }

      // Get the session
      const session = await storage.getAccessSession(parseInt(sessionId));
      if (!session) {
        return res.status(404).json({ message: "Session not found" });
      }

      // Calculate new grace end time
      const now = Date.now();
      const newGraceEnd = now + (gracePeriodMinutes * 60 * 1000);

      // Update the session
      const updatedSession = await storage.updateAccessSession(parseInt(sessionId), {
        graceEnd: newGraceEnd,
        status: gracePeriodMinutes > 0 ? 'grace_period' : 'expired'
      });

      console.log(`Grace period adjusted for session ${sessionId}:`, {
        sessionId: parseInt(sessionId),
        userId: session.userId,
        bookingId: session.bookingId,
        gracePeriodMinutes,
        newGraceEnd,
        newStatus: gracePeriodMinutes > 0 ? 'grace_period' : 'expired',
        updatedSession: updatedSession ? 'success' : 'failed'
      });

      // Update the Swift Agent Service timers
      if (swiftAgentService) {
        await swiftAgentService.updateSessionGracePeriod(parseInt(sessionId), gracePeriodMinutes);
      }

      // Send notification about grace period adjustment
      const notification = {
        type: 'grace_period_adjusted',
        data: {
          sessionId: parseInt(sessionId),
          userId: session.userId,
          newGracePeriodMinutes: gracePeriodMinutes,
          newGraceEnd,
          adjustedBy: 'admin',
          timestamp: now
        }
      };

      // Broadcast to all connected clients
      wss.clients.forEach((client) => {
        if (client.readyState === WebSocket.OPEN) {
          client.send(JSON.stringify(notification));
        }
      });

      res.json({
        success: true,
        message: "Grace period adjusted successfully",
        newGraceEnd,
        gracePeriodMinutes
      });

    } catch (error) {
      console.error("Error adjusting grace period:", error);
      res.status(500).json({ message: "Failed to adjust grace period" });
    }
  });

  // Extend booking time for expired bookings
  app.post("/api/admin/bookings/:bookingId/extend", async (req, res) => {
    try {
      const { bookingId } = req.params;
      const { extensionMinutes } = req.body;

      if (!extensionMinutes || extensionMinutes < 15 || extensionMinutes > 1440) {
        return res.status(400).json({ message: "Invalid extension time. Must be between 15 minutes and 24 hours." });
      }

      // Get the booking
      const booking = await storage.getBooking(parseInt(bookingId));
      if (!booking) {
        return res.status(404).json({ message: "Booking not found" });
      }

      // Calculate new end time
      const extensionMs = extensionMinutes * 60 * 1000;
      const newEndTime = booking.endTime + extensionMs;

      // Update the booking
      await storage.updateBooking(parseInt(bookingId), {
        endTime: newEndTime,
        duration: booking.duration + extensionMinutes
      });

      // Update locker status back to occupied if it was expired
      await storage.updateLockerStatus(booking.lockerId, "occupied");

      // Log activity
      await storage.createActivity({
        type: "booking",
        message: `Booking ${bookingId} extended by ${extensionMinutes} minutes by admin`,
        lockerId: booking.lockerId,
        userId: booking.userId,
        metadata: JSON.stringify({
          extensionMinutes,
          newEndTime,
          extendedBy: 'admin',
          timestamp: new Date().toISOString()
        }),
      });

      // Send notification about booking extension
      const notification = {
        type: 'booking_extended',
        data: {
          bookingId: parseInt(bookingId),
          userId: booking.userId,
          extensionMinutes,
          newEndTime,
          extendedBy: 'admin',
          timestamp: Date.now()
        }
      };

      // Broadcast to all connected clients
      wss.clients.forEach((client) => {
        if (client.readyState === WebSocket.OPEN) {
          client.send(JSON.stringify(notification));
        }
      });

      res.json({
        success: true,
        message: "Booking extended successfully",
        newEndTime,
        extensionMinutes
      });

    } catch (error) {
      console.error("Error extending booking:", error);
      res.status(500).json({ message: "Failed to extend booking" });
    }
  });

  return httpServer;
}
