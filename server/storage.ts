import {
  users,
  lockers,
  locations,
  bookings,
  tasks,
  activities,
  chatMessages,
  agentBiometrics,
  lockerAccessSessions,
  expirationNotifications,
  sensorReadings,
  maintenanceRecords,
  subscriptionHistory,
  auditLogs,
  adminSessions,
  alertRules,
  scheduledReports,
  apiUsageTracking,
  apiUsageLogs,
  rateLimitRules,
  maintenanceSchedules,
  notificationTemplates,
  integrations,
  type User,
  type UpsertUser,
  type Locker,
  type InsertLocker,
  type Location,
  type InsertLocation,
  type Booking,
  type InsertBooking,
  type Task,
  type InsertTask,
  type Activity,
  type InsertActivity,
  type ChatMessage,
  type InsertChatMessage,
  type AgentBiometric,
  type InsertAgentBiometric,
  type LockerAccessSession,
  type InsertLockerAccessSession,
  type ExpirationNotification,
  type InsertExpirationNotification,
} from "@shared/schema";
import { db } from "./db";
import { eq, desc, and, or, count, sql } from "drizzle-orm";

export interface IStorage {
  // User operations
  getUser(id: string): Promise<User | undefined>;
  upsertUser(user: UpsertUser): Promise<User>;
  getUsersByRole(role: string): Promise<User[]>;
  updateUserStatus(id: string, status: string): Promise<User | undefined>;
  updateUserVerification(id: string, verified: boolean): Promise<User | undefined>;
  updateUser(id: string, userData: Partial<User>): Promise<User | undefined>;
  deleteUser(id: string): Promise<boolean>;

  // Locker operations
  getLocker(id: number): Promise<Locker | undefined>;
  getLockerByCode(code: string): Promise<Locker | undefined>;
  getLockers(): Promise<Locker[]>;
  getLockersByStatus(status: string): Promise<Locker[]>;
  createLocker(locker: InsertLocker): Promise<Locker>;
  updateLockerStatus(id: number, status: string): Promise<Locker | undefined>;
  deleteLocker(id: number): Promise<boolean>;

  // Location operations
  getLocation(id: number): Promise<Location | undefined>;
  getLocations(): Promise<Location[]>;
  createLocation(location: InsertLocation): Promise<Location>;

  // Booking operations
  getBooking(id: number): Promise<Booking | undefined>;
  getBookings(): Promise<Booking[]>;
  getBookingsByUser(userId: string): Promise<Booking[]>;
  getBookingsByAgent(agentId: string): Promise<Booking[]>;
  createBooking(booking: InsertBooking): Promise<Booking>;
  updateBooking(id: number, updates: Partial<Booking>): Promise<Booking | undefined>;
  updateBookingStatus(id: number, status: string): Promise<Booking | undefined>;

  // Task operations
  getTask(id: number): Promise<Task | undefined>;
  getTasks(): Promise<Task[]>;
  getTasksByAgent(agentId: string): Promise<Task[]>;
  createTask(task: InsertTask): Promise<Task>;
  updateTaskStatus(id: number, status: string): Promise<Task | undefined>;

  // Activity operations
  getActivities(limit?: number): Promise<Activity[]>;
  createActivity(activity: InsertActivity): Promise<Activity>;

  // Chat operations
  getChatMessages(fromUserId: string, toUserId: string, bookingId?: number): Promise<ChatMessage[]>;
  createChatMessage(message: InsertChatMessage): Promise<ChatMessage>;

  // Statistics
  getStats(): Promise<{
    totalLockers: number;
    occupiedLockers: number;
    availableLockers: number;
    totalUsers: number;
    activeAgents: number;
    activeAlerts: number;
  }>;

  // Swift Agent Biometric operations
  getAgentBiometric(agentId: string, biometricType: string): Promise<AgentBiometric | undefined>;
  getActiveBiometricsByType(biometricType: string): Promise<AgentBiometric[]>;
  createAgentBiometric(biometric: InsertAgentBiometric): Promise<AgentBiometric>;
  updateAgentBiometricLastUsed(id: number): Promise<AgentBiometric | undefined>;
  deactivateAgentBiometric(id: number): Promise<AgentBiometric | undefined>;

  // Locker Access Session operations
  getLockerAccessSession(id: number): Promise<LockerAccessSession | undefined>;
  getActiveAccessSessions(): Promise<LockerAccessSession[]>;
  getAccessSessionsByUser(userId: string): Promise<LockerAccessSession[]>;
  getAccessSessionsByAgent(agentId: string): Promise<LockerAccessSession[]>;
  createLockerAccessSession(session: InsertLockerAccessSession): Promise<LockerAccessSession>;
  updateLockerAccessSession(id: number, updates: Partial<LockerAccessSession>): Promise<LockerAccessSession | undefined>;

  // Expiration Notification operations
  getExpirationNotifications(sessionId: number): Promise<ExpirationNotification[]>;
  createExpirationNotification(notification: InsertExpirationNotification): Promise<ExpirationNotification>;
  markNotificationAcknowledged(id: number): Promise<ExpirationNotification | undefined>;

  // Additional helper methods
  getUserById(id: string): Promise<User | undefined>;
  getBookingById(id: number): Promise<Booking | undefined>;
  checkSwiftAgentTables(): Promise<boolean>;
}

export class DatabaseStorage implements IStorage {
  // User operations
  async getUser(id: string): Promise<User | undefined> {
    try {
      const [user] = await db.select().from(users).where(eq(users.id, id));
      return user;
    } catch (error) {
      console.error("Database error in getUser:", error);
      throw new Error("Database connection failed");
    }
  }

  async upsertUser(userData: UpsertUser): Promise<User> {
    try {
      const [user] = await db
        .insert(users)
        .values(userData)
        .onConflictDoUpdate({
          target: users.id,
          set: {
            ...userData,
            updatedAt: Date.now(),
          },
        })
        .returning();
      return user;
    } catch (error) {
      console.error("Database error in upsertUser:", error);
      throw new Error("Failed to create/update user");
    }
  }

  async getUsersByRole(role: string): Promise<User[]> {
    return await db.select().from(users).where(eq(users.role, role));
  }

  async updateUserStatus(id: string, status: string): Promise<User | undefined> {
    try {
      const [updatedUser] = await db
        .update(users)
        .set({ status, updatedAt: Date.now() })
        .where(eq(users.id, id))
        .returning();
      return updatedUser;
    } catch (error) {
      console.error("Database error in updateUserStatus:", error);
      return undefined;
    }
  }

  async updateUserVerification(id: string, verified: boolean): Promise<User | undefined> {
    try {
      const [updatedUser] = await db
        .update(users)
        .set({ verificationStatus: verified, updatedAt: Date.now() })
        .where(eq(users.id, id))
        .returning();
      return updatedUser;
    } catch (error) {
      console.error("Database error in updateUserVerification:", error);
      return undefined;
    }
  }

  async updateUser(id: string, userData: Partial<User>): Promise<User | undefined> {
    try {
      const updateData = {
        ...userData,
        updatedAt: Date.now()
      };

      // Remove id from update data to prevent conflicts
      delete updateData.id;
      delete updateData.createdAt;

      const [updatedUser] = await db
        .update(users)
        .set(updateData)
        .where(eq(users.id, id))
        .returning();
      return updatedUser;
    } catch (error) {
      console.error("Database error in updateUser:", error);
      return undefined;
    }
  }

  async deleteUser(id: string): Promise<boolean> {
    try {
      // Use raw SQL to delete user and all related records with foreign keys disabled
      const result = await db.transaction(async (tx) => {
        // Disable foreign key constraints
        await tx.run(sql`PRAGMA foreign_keys = OFF`);

        // Delete all related records using raw SQL for better control
        const deleteQueries = [
          // Delete expiration notifications
          sql`DELETE FROM expiration_notifications WHERE user_id = ${id} OR agent_id = ${id}`,

          // Delete locker access sessions
          sql`DELETE FROM locker_access_sessions WHERE user_id = ${id} OR agent_id = ${id}`,

          // Delete agent biometrics
          sql`DELETE FROM agent_biometrics WHERE agent_id = ${id}`,

          // Delete chat messages
          sql`DELETE FROM chat_messages WHERE from_user_id = ${id} OR to_user_id = ${id}`,

          // Delete activities
          sql`DELETE FROM activities WHERE user_id = ${id}`,

          // Delete tasks
          sql`DELETE FROM tasks WHERE agent_id = ${id}`,

          // Delete bookings where user is customer
          sql`DELETE FROM bookings WHERE user_id = ${id}`,

          // Update bookings to remove agent reference
          sql`UPDATE bookings SET agent_id = NULL WHERE agent_id = ${id}`,

          // Delete subscription history
          sql`DELETE FROM subscription_history WHERE user_id = ${id}`,

          // Delete audit logs
          sql`DELETE FROM audit_logs WHERE user_id = ${id}`,

          // Delete admin sessions
          sql`DELETE FROM admin_sessions WHERE user_id = ${id}`,

          // Delete alert rules
          sql`DELETE FROM alert_rules WHERE created_by = ${id}`,

          // Delete scheduled reports
          sql`DELETE FROM scheduled_reports WHERE created_by = ${id}`,

          // Delete API usage tracking
          sql`DELETE FROM api_usage_tracking WHERE user_id = ${id}`,

          // Delete API usage logs
          sql`DELETE FROM api_usage_logs WHERE user_id = ${id}`,

          // Delete rate limit rules
          sql`DELETE FROM rate_limit_rules WHERE created_by = ${id}`,

          // Delete maintenance schedules
          sql`DELETE FROM maintenance_schedules WHERE assigned_to = ${id} OR created_by = ${id}`,

          // Delete notification templates
          sql`DELETE FROM notification_templates WHERE created_by = ${id}`,

          // Delete integrations
          sql`DELETE FROM integrations WHERE created_by = ${id}`,

          // Delete maintenance records
          sql`DELETE FROM maintenance_records WHERE technician_id = ${id}`,
        ];

        // Execute all delete queries, ignoring errors for non-existent tables
        for (const query of deleteQueries) {
          try {
            await tx.run(query);
          } catch (error) {
            console.log(`Ignoring error for query: ${error}`);
          }
        }

        // Finally, delete the user
        const userDeleteResult = await tx.run(sql`DELETE FROM users WHERE id = ${id}`);

        // Re-enable foreign key constraints
        await tx.run(sql`PRAGMA foreign_keys = ON`);

        return userDeleteResult.changes > 0;
      });

      return result;
    } catch (error) {
      console.error("Database error in deleteUser:", error);
      return false;
    }
  }

  // Locker operations
  async getLocker(id: number): Promise<Locker | undefined> {
    const [locker] = await db.select().from(lockers).where(eq(lockers.id, id));
    return locker;
  }

  async getLockerByCode(code: string): Promise<Locker | undefined> {
    const [locker] = await db.select().from(lockers).where(eq(lockers.code, code));
    return locker;
  }

  async getLockers(): Promise<Locker[]> {
    try {
      return await db.select().from(lockers);
    } catch (error) {
      console.error("Database error in getLockers:", error);
      throw error;
    }
  }

  async getLockersByStatus(status: string): Promise<Locker[]> {
    return await db.select().from(lockers).where(eq(lockers.status, status));
  }

  async createLocker(locker: InsertLocker): Promise<Locker> {
    const [newLocker] = await db.insert(lockers).values(locker).returning();
    return newLocker;
  }

  async updateLockerStatus(id: number, status: string): Promise<Locker | undefined> {
    try {
      const [updatedLocker] = await db
        .update(lockers)
        .set({ status, updatedAt: Date.now() })
        .where(eq(lockers.id, id))
        .returning();
      return updatedLocker;
    } catch (error) {
      console.error("Database error in updateLockerStatus:", error);
      return undefined;
    }
  }

  async updateLockerPing(id: number): Promise<Locker | undefined> {
    try {
      const [updatedLocker] = await db
        .update(lockers)
        .set({ lastPing: Date.now(), updatedAt: Date.now() })
        .where(eq(lockers.id, id))
        .returning();
      return updatedLocker;
    } catch (error) {
      console.error("Database error in updateLockerPing:", error);
      return undefined;
    }
  }

  async deleteLocker(id: number): Promise<boolean> {
    try {
      // Check if locker exists first
      const existingLocker = await db
        .select()
        .from(lockers)
        .where(eq(lockers.id, id))
        .limit(1);

      if (existingLocker.length === 0) {
        throw new Error("Locker not found");
      }

      // Check if locker has active bookings
      const activeBookings = await db
        .select()
        .from(bookings)
        .where(and(
          eq(bookings.lockerId, id),
          or(
            eq(bookings.status, 'active'),
            eq(bookings.status, 'confirmed')
          )
        ));

      if (activeBookings.length > 0) {
        throw new Error("Cannot delete locker with active bookings");
      }

      // Temporarily disable foreign key constraints
      db.$client.pragma('foreign_keys = OFF');

      try {
        // Delete related records first (in order to avoid foreign key constraints)

        // 1. Get all bookings for this locker first
        const lockerBookings = await db
          .select({ id: bookings.id })
          .from(bookings)
          .where(eq(bookings.lockerId, id));

        // 2. Delete tasks related to bookings for this locker
        for (const booking of lockerBookings) {
          await db.delete(tasks).where(eq(tasks.bookingId, booking.id));
        }

        // 3. Get all access sessions for this locker
        const lockerSessions = await db
          .select({ id: lockerAccessSessions.id })
          .from(lockerAccessSessions)
          .where(eq(lockerAccessSessions.lockerId, id));

        // 4. Delete expiration notifications for sessions related to this locker
        for (const session of lockerSessions) {
          await db.delete(expirationNotifications).where(eq(expirationNotifications.sessionId, session.id));
        }

        // 5. Delete locker access sessions
        await db.delete(lockerAccessSessions).where(eq(lockerAccessSessions.lockerId, id));

        // 6. Delete sensor readings for this locker (only if table exists)
        try {
          await db.delete(sensorReadings).where(eq(sensorReadings.lockerId, id));
        } catch (error) {
          // Table might not exist, continue silently
        }

        // 7. Delete maintenance records for this locker (only if table exists)
        try {
          await db.delete(maintenanceRecords).where(eq(maintenanceRecords.lockerId, id));
        } catch (error) {
          // Table might not exist, continue silently
        }

        // 8. Delete bookings for this locker
        await db.delete(bookings).where(eq(bookings.lockerId, id));

        // 9. Delete activities related to this locker
        await db.delete(activities).where(eq(activities.lockerId, id));

        // 10. Finally, delete the locker
        const deleteResult = await db.delete(lockers).where(eq(lockers.id, id));

        return deleteResult.changes > 0;
      } finally {
        // Re-enable foreign key constraints
        db.$client.pragma('foreign_keys = ON');
      }
    } catch (error) {
      console.error("Database error in deleteLocker:", error);
      // If the locker was actually deleted despite the error, return true
      const lockerStillExists = await db
        .select()
        .from(lockers)
        .where(eq(lockers.id, id))
        .limit(1);

      if (lockerStillExists.length === 0) {
        console.log(`Locker ${id} was successfully deleted despite error`);
        return true;
      }

      throw error;
    }
  }

  // Location operations
  async getLocation(id: number): Promise<Location | undefined> {
    const [location] = await db.select().from(locations).where(eq(locations.id, id));
    return location;
  }

  async getLocations(): Promise<Location[]> {
    return await db.select().from(locations);
  }

  async createLocation(location: InsertLocation): Promise<Location> {
    const [newLocation] = await db.insert(locations).values(location).returning();
    return newLocation;
  }

  // Booking operations
  async getBooking(id: number): Promise<Booking | undefined> {
    const [booking] = await db.select().from(bookings).where(eq(bookings.id, id));
    return booking;
  }

  async getBookings(): Promise<Booking[]> {
    return await db.select().from(bookings).orderBy(desc(bookings.createdAt));
  }

  async getBookingsByUser(userId: string): Promise<Booking[]> {
    return await db
      .select()
      .from(bookings)
      .where(eq(bookings.userId, userId))
      .orderBy(desc(bookings.createdAt));
  }

  async getBookingsByAgent(agentId: string): Promise<Booking[]> {
    return await db
      .select()
      .from(bookings)
      .where(eq(bookings.agentId, agentId))
      .orderBy(desc(bookings.createdAt));
  }

  async createBooking(booking: InsertBooking): Promise<Booking> {
    const [newBooking] = await db.insert(bookings).values(booking).returning();
    return newBooking;
  }

  async updateBooking(id: number, updates: Partial<Booking>): Promise<Booking | undefined> {
    try {
      const [updatedBooking] = await db
        .update(bookings)
        .set({ ...updates, updatedAt: Date.now() })
        .where(eq(bookings.id, id))
        .returning();
      return updatedBooking;
    } catch (error) {
      console.error("Database error in updateBooking:", error);
      return undefined;
    }
  }

  async updateBookingStatus(id: number, status: string): Promise<Booking | undefined> {
    try {
      const [updatedBooking] = await db
        .update(bookings)
        .set({ status, updatedAt: Date.now() })
        .where(eq(bookings.id, id))
        .returning();
      return updatedBooking;
    } catch (error) {
      console.error("Database error in updateBookingStatus:", error);
      return undefined;
    }
  }

  // Task operations
  async getTask(id: number): Promise<Task | undefined> {
    const [task] = await db.select().from(tasks).where(eq(tasks.id, id));
    return task;
  }

  async getTasks(): Promise<Task[]> {
    return await db.select().from(tasks).orderBy(desc(tasks.createdAt));
  }

  async getTasksByAgent(agentId: string): Promise<Task[]> {
    return await db
      .select()
      .from(tasks)
      .where(eq(tasks.agentId, agentId))
      .orderBy(desc(tasks.createdAt));
  }

  async createTask(task: InsertTask): Promise<Task> {
    const [newTask] = await db.insert(tasks).values(task).returning();
    return newTask;
  }

  async updateTaskStatus(id: number, status: string): Promise<Task | undefined> {
    try {
      const [updatedTask] = await db
        .update(tasks)
        .set({ status, updatedAt: Date.now() })
        .where(eq(tasks.id, id))
        .returning();
      return updatedTask;
    } catch (error) {
      console.error("Database error in updateTaskStatus:", error);
      return undefined;
    }
  }

  // Activity operations
  async getActivities(limit = 20): Promise<Activity[]> {
    return await db
      .select()
      .from(activities)
      .orderBy(desc(activities.createdAt))
      .limit(limit);
  }

  async createActivity(activity: InsertActivity): Promise<Activity> {
    const [newActivity] = await db.insert(activities).values(activity).returning();
    return newActivity;
  }

  // Chat operations
  async getChatMessages(fromUserId: string, toUserId: string, bookingId?: number): Promise<ChatMessage[]> {
    const whereClause = bookingId
      ? and(
          or(
            and(eq(chatMessages.fromUserId, fromUserId), eq(chatMessages.toUserId, toUserId)),
            and(eq(chatMessages.fromUserId, toUserId), eq(chatMessages.toUserId, fromUserId))
          ),
          eq(chatMessages.bookingId, bookingId)
        )
      : or(
          and(eq(chatMessages.fromUserId, fromUserId), eq(chatMessages.toUserId, toUserId)),
          and(eq(chatMessages.fromUserId, toUserId), eq(chatMessages.toUserId, fromUserId))
        );

    const messages = await db
      .select({
        id: chatMessages.id,
        fromUserId: chatMessages.fromUserId,
        toUserId: chatMessages.toUserId,
        bookingId: chatMessages.bookingId,
        message: chatMessages.message,
        isRead: chatMessages.isRead,
        readAt: chatMessages.readAt,
        createdAt: chatMessages.createdAt,
        fromUser: {
          id: users.id,
          username: users.username,
          role: users.role,
          firstName: users.firstName,
          lastName: users.lastName,
        }
      })
      .from(chatMessages)
      .leftJoin(users, eq(chatMessages.fromUserId, users.id))
      .where(whereClause)
      .orderBy(chatMessages.createdAt);

    return messages as ChatMessage[];
  }

  async createChatMessage(messageData: InsertChatMessage): Promise<ChatMessage> {
    const [message] = await db
      .insert(chatMessages)
      .values(messageData)
      .returning();
    return message;
  }

  async getSupportConversations(adminId: string): Promise<any[]> {
    console.log("getSupportConversations called with adminId:", adminId);

    // Get all unique users who have sent messages to this admin
    const conversations = await db
      .select({
        userId: chatMessages.fromUserId,
        user: {
          id: users.id,
          username: users.username,
          role: users.role,
          firstName: users.firstName,
          lastName: users.lastName,
        },
        lastMessage: {
          id: chatMessages.id,
          message: chatMessages.message,
          createdAt: chatMessages.createdAt,
          fromUserId: chatMessages.fromUserId,
          isRead: chatMessages.isRead,
        }
      })
      .from(chatMessages)
      .innerJoin(users, eq(chatMessages.fromUserId, users.id))
      .where(
        or(
          eq(chatMessages.toUserId, adminId),
          eq(chatMessages.fromUserId, adminId)
        )
      )
      .orderBy(desc(chatMessages.createdAt));

    console.log("Raw conversations from DB:", conversations.length);

    // Group by user and get the latest message for each conversation
    const conversationMap = new Map();

    for (const conv of conversations) {
      const userId = conv.userId === adminId ? conv.lastMessage.fromUserId : conv.userId;

      if (userId === adminId) continue; // Skip admin's own messages

      if (!conversationMap.has(userId) ||
          conversationMap.get(userId).lastMessage.createdAt < (conv.lastMessage.createdAt ?? 0)) {

        // Count unread messages from this user to admin
        const unreadCount = await db
          .select({ count: count() })
          .from(chatMessages)
          .where(
            and(
              eq(chatMessages.fromUserId, userId),
              eq(chatMessages.toUserId, adminId),
              eq(chatMessages.isRead, false)
            )
          );

        conversationMap.set(userId, {
          userId,
          user: conv.user,
          lastMessage: conv.lastMessage,
          unreadCount: unreadCount[0]?.count || 0
        });
      }
    }

    return Array.from(conversationMap.values())
      .sort((a, b) => b.lastMessage.createdAt - a.lastMessage.createdAt);
  }

  async markMessagesAsRead(fromUserId: string, toUserId: string): Promise<void> {
    await db
      .update(chatMessages)
      .set({
        isRead: true,
        readAt: Date.now()
      })
      .where(
        and(
          eq(chatMessages.fromUserId, fromUserId),
          eq(chatMessages.toUserId, toUserId),
          eq(chatMessages.isRead, false)
        )
      );
  }

  // Statistics
  async getStats(): Promise<{
    totalLockers: number;
    occupiedLockers: number;
    availableLockers: number;
    totalUsers: number;
    activeAgents: number;
    activeAlerts: number;
  }> {
    const [lockerStats] = await db
      .select({
        total: count(),
        occupied: sql<number>`count(case when status = 'occupied' then 1 end)`,
        available: sql<number>`count(case when status = 'available' then 1 end)`,
      })
      .from(lockers);

    const [userStats] = await db
      .select({
        total: count(),
        agents: sql<number>`count(case when role = 'agent' then 1 end)`,
      })
      .from(users);

    const [alertStats] = await db
      .select({
        alerts: count(),
      })
      .from(activities)
      .where(eq(activities.type, "alert"));

    return {
      totalLockers: lockerStats.total,
      occupiedLockers: lockerStats.occupied,
      availableLockers: lockerStats.available,
      totalUsers: userStats.total,
      activeAgents: userStats.agents,
      activeAlerts: alertStats.alerts,
    };
  }

  // Swift Agent Biometric operations
  async getAgentBiometric(agentId: string, biometricType: string): Promise<AgentBiometric | undefined> {
    try {
      const [biometric] = await db
        .select()
        .from(agentBiometrics)
        .where(
          and(
            eq(agentBiometrics.agentId, agentId),
            eq(agentBiometrics.biometricType, biometricType),
            eq(agentBiometrics.isActive, true)
          )
        );
      return biometric;
    } catch (error) {
      console.error("Database error in getAgentBiometric:", error);
      return undefined;
    }
  }

  async getActiveBiometricsByType(biometricType: string): Promise<AgentBiometric[]> {
    try {
      return await db
        .select()
        .from(agentBiometrics)
        .where(
          and(
            eq(agentBiometrics.biometricType, biometricType),
            eq(agentBiometrics.isActive, true)
          )
        );
    } catch (error) {
      console.error("Database error in getActiveBiometricsByType:", error);
      return [];
    }
  }

  async createAgentBiometric(biometric: InsertAgentBiometric): Promise<AgentBiometric> {
    try {
      const [newBiometric] = await db
        .insert(agentBiometrics)
        .values(biometric)
        .returning();
      return newBiometric;
    } catch (error) {
      console.error("Database error in createAgentBiometric:", error);
      throw new Error("Failed to create agent biometric");
    }
  }

  async updateAgentBiometricLastUsed(id: number): Promise<AgentBiometric | undefined> {
    try {
      const [updatedBiometric] = await db
        .update(agentBiometrics)
        .set({ lastUsed: Date.now(), updatedAt: Date.now() })
        .where(eq(agentBiometrics.id, id))
        .returning();
      return updatedBiometric;
    } catch (error) {
      console.error("Database error in updateAgentBiometricLastUsed:", error);
      return undefined;
    }
  }

  async deactivateAgentBiometric(id: number): Promise<AgentBiometric | undefined> {
    try {
      const [updatedBiometric] = await db
        .update(agentBiometrics)
        .set({ isActive: false, updatedAt: Date.now() })
        .where(eq(agentBiometrics.id, id))
        .returning();
      return updatedBiometric;
    } catch (error) {
      console.error("Database error in deactivateAgentBiometric:", error);
      return undefined;
    }
  }

  // Locker Access Session operations
  async getLockerAccessSession(id: number): Promise<LockerAccessSession | undefined> {
    try {
      const [session] = await db
        .select()
        .from(lockerAccessSessions)
        .where(eq(lockerAccessSessions.id, id));
      return session;
    } catch (error) {
      console.error("Database error in getLockerAccessSession:", error);
      return undefined;
    }
  }

  async getActiveAccessSessions(): Promise<LockerAccessSession[]> {
    try {
      return await db
        .select()
        .from(lockerAccessSessions)
        .where(
          or(
            eq(lockerAccessSessions.status, "active"),
            eq(lockerAccessSessions.status, "grace_period")
          )
        )
        .orderBy(desc(lockerAccessSessions.createdAt));
    } catch (error) {
      console.error("Database error in getActiveAccessSessions:", error);
      return [];
    }
  }

  async getAccessSessionsByUser(userId: string): Promise<LockerAccessSession[]> {
    try {
      return await db
        .select()
        .from(lockerAccessSessions)
        .where(eq(lockerAccessSessions.userId, userId))
        .orderBy(desc(lockerAccessSessions.createdAt));
    } catch (error) {
      console.error("Database error in getAccessSessionsByUser:", error);
      return [];
    }
  }

  async getAccessSessionsByAgent(agentId: string): Promise<LockerAccessSession[]> {
    try {
      return await db
        .select()
        .from(lockerAccessSessions)
        .where(eq(lockerAccessSessions.agentId, agentId))
        .orderBy(desc(lockerAccessSessions.createdAt));
    } catch (error) {
      console.error("Database error in getAccessSessionsByAgent:", error);
      return [];
    }
  }

  async createLockerAccessSession(session: InsertLockerAccessSession): Promise<LockerAccessSession> {
    try {
      const [newSession] = await db
        .insert(lockerAccessSessions)
        .values(session)
        .returning();
      return newSession;
    } catch (error) {
      console.error("Database error in createLockerAccessSession:", error);
      throw new Error("Failed to create locker access session");
    }
  }

  async updateLockerAccessSession(id: number, updates: Partial<LockerAccessSession>): Promise<LockerAccessSession | undefined> {
    try {
      const updateData = {
        ...updates,
        updatedAt: Date.now()
      };

      // Remove id and createdAt from update data to prevent conflicts
      delete updateData.id;
      delete updateData.createdAt;

      const [updatedSession] = await db
        .update(lockerAccessSessions)
        .set(updateData)
        .where(eq(lockerAccessSessions.id, id))
        .returning();
      return updatedSession;
    } catch (error) {
      console.error("Database error in updateLockerAccessSession:", error);
      return undefined;
    }
  }

  // Expiration Notification operations
  async getExpirationNotifications(sessionId: number): Promise<ExpirationNotification[]> {
    try {
      return await db
        .select()
        .from(expirationNotifications)
        .where(eq(expirationNotifications.sessionId, sessionId))
        .orderBy(desc(expirationNotifications.createdAt));
    } catch (error) {
      console.error("Database error in getExpirationNotifications:", error);
      return [];
    }
  }

  async createExpirationNotification(notification: InsertExpirationNotification): Promise<ExpirationNotification> {
    try {
      const [newNotification] = await db
        .insert(expirationNotifications)
        .values(notification)
        .returning();
      return newNotification;
    } catch (error) {
      console.error("Database error in createExpirationNotification:", error);
      throw new Error("Failed to create expiration notification");
    }
  }

  async markNotificationAcknowledged(id: number): Promise<ExpirationNotification | undefined> {
    try {
      const [updatedNotification] = await db
        .update(expirationNotifications)
        .set({ acknowledged: true, acknowledgedAt: Date.now() })
        .where(eq(expirationNotifications.id, id))
        .returning();
      return updatedNotification;
    } catch (error) {
      console.error("Database error in markNotificationAcknowledged:", error);
      return undefined;
    }
  }

  // Additional helper methods
  async getUserById(id: string): Promise<User | undefined> {
    return this.getUser(id);
  }

  async getBookingById(id: number): Promise<Booking | undefined> {
    return this.getBooking(id);
  }

  async checkSwiftAgentTables(): Promise<boolean> {
    try {
      // Check if all Swift Agent tables exist
      const tables = ['agent_biometrics', 'locker_access_sessions', 'expiration_notifications'];

      for (const tableName of tables) {
        const tableExists = await db
          .select({ name: sql`name` })
          .from(sql`sqlite_master`)
          .where(sql`type = 'table' AND name = ${tableName}`)
          .limit(1);

        if (tableExists.length === 0) {
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error("Error checking Swift Agent tables:", error);
      return false;
    }
  }

  // Additional methods for admin functionality
  async getBookingsByLocker(lockerId: number): Promise<Booking[]> {
    return await db
      .select()
      .from(bookings)
      .where(eq(bookings.lockerId, lockerId))
      .orderBy(desc(bookings.createdAt));
  }

  /**
   * Calculate the dynamic status of a locker based on current conditions
   */
  async calculateLockerStatus(locker: any, activeBooking?: any, accessSession?: any): Promise<string> {
    const now = Date.now();

    // If no active booking, locker is available
    if (!activeBooking) {
      return "available";
    }

    // Check if booking has ended
    const bookingEnded = now > activeBooking.endTime;

    // If there's an access session, use its status to determine locker status
    if (accessSession) {
      switch (accessSession.status) {
        case 'active':
          // Booking is active and within time limits
          return bookingEnded ? "expired" : "occupied";
        case 'expired':
          // Session has expired but might be in grace period
          return "expired";
        case 'grace_period':
          // In grace period - still accessible but expired
          return "expired";
        case 'revoked':
          // Access has been revoked
          return "available";
        default:
          // Fallback to time-based calculation
          return bookingEnded ? "expired" : "occupied";
      }
    }

    // If no access session but there's an active booking
    if (activeBooking.status === 'active' || activeBooking.status === 'confirmed') {
      return bookingEnded ? "expired" : "occupied";
    }

    // Default fallback
    return "available";
  }

  async getTasksByBooking(bookingId: number): Promise<Task[]> {
    return await db
      .select()
      .from(tasks)
      .where(eq(tasks.bookingId, bookingId))
      .orderBy(desc(tasks.createdAt));
  }

  async getAccessSession(sessionId: number): Promise<LockerAccessSession | undefined> {
    const [session] = await db
      .select()
      .from(lockerAccessSessions)
      .where(eq(lockerAccessSessions.id, sessionId));
    return session;
  }

  async updateAccessSession(sessionId: number, updates: Partial<LockerAccessSession>): Promise<LockerAccessSession | undefined> {
    try {
      const [updatedSession] = await db
        .update(lockerAccessSessions)
        .set({ ...updates, updatedAt: Date.now() })
        .where(eq(lockerAccessSessions.id, sessionId))
        .returning();
      return updatedSession;
    } catch (error) {
      console.error("Database error in updateAccessSession:", error);
      return undefined;
    }
  }
}

export const storage = new DatabaseStorage();
